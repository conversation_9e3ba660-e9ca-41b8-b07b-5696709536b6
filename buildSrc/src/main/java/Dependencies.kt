object MavenRun {
    const val isPushSource = true//是否上传源代码(这样依赖下来就可以直接看到库里面的代码)
    const val url = "https://packages.aliyun.com/maven/repository/2160068-release-Q0kcTH/"
    const val userName = "650e5bf5a220ae99aea1309c"
    const val password = "]qL0i2kqEMrP"
//    const val mavenVersion = "1.2.8-SNAPSHOT"
    const val mavenVersion = "1.2.8"
}


object MinifyEnabled {
    const val release = true
    const val debug = true
}

object AndroidVersions {
    const val kotlinVersion = "1.8.21"
    const val compileSdkVersion = 34
    const val minSdkVersion = 22
    const val targetSdkVersion = 34
}

object Dependencies {
    const val appcompat = "1.5.1"//androidx.appcompat:appcompat:
    const val material = "1.9.0"//com.google.android.material:material:
    const val coreKtx = "1.9.0"//androidx.core:core-ktx:
    const val coroutines = "1.6.1"//org.jetbrains.kotlinx:kotlinx-coroutines-android:
    const val lifecycleViewModelKtx = "2.5.1"//androidx.lifecycle:lifecycle-viewmodel-ktx:
    const val lifecycleRuntimeKtx = "2.4.0"//androidx.lifecycle:lifecycle-runtime-ktx:
    const val lifecycleCommonKtx = "2.2.0"//androidx.lifecycle:lifecycle-common-java8:
    const val permissionX = "1.8.1"//com.guolindev.permissionx:permissionx:1.7.1
    const val log = "1.2.5"//com.github.oooo7777777:Vlog:1.2.1
}

