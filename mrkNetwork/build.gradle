plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'maven-publish'
    id 'com.kezong.fat-aar'
}
apply from: "upload.gradle"

android {
    namespace 'com.mrk.network'
    compileSdk AndroidVersions.targetSdkVersion

    defaultConfig {
        minSdk AndroidVersions.minSdkVersion
        targetSdk AndroidVersions.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-reflect:${AndroidVersions.kotlinVersion}"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:${Dependencies.lifecycleViewModelKtx}"
    implementation "androidx.lifecycle:lifecycle-common-java8:${Dependencies.lifecycleCommonKtx}"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:${Dependencies.lifecycleRuntimeKtx}"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:${Dependencies.coroutines}"
    implementation "com.google.android.material:material:${Dependencies.material}"

    //retrofit
    api "com.squareup.retrofit2:retrofit:2.9.0"//retrofit
    api "com.jakewharton.retrofit:retrofit2-kotlin-coroutines-adapter:0.9.2"//retrofit
    //fastjson
    api "com.alibaba:fastjson:1.2.83"

    //日志打印
    api "com.github.oooo7777777:Vlog:${Dependencies.log}"
    api 'com.kunminx.arch:unpeek-livedata:7.8.0'
}