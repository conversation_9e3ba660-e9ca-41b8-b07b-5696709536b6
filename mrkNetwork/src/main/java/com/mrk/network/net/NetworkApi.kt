package com.mrk.network.net

import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.*

/**
 * author  : ww
 * desc    : 自定义网络请求
 * time    : 2021-12-21 16:27:46
 */
interface NetworkApi {
    @GET
    suspend fun get(@Url url: String): String

    @GET
    suspend fun get(@Url url: String, @QueryMap map: Map<String, @JvmSuppressWildcards Any>): String

    @POST
    suspend fun post(@Url url: String): String

    @POST
    suspend fun post(@Url Path: String, @Body body: RequestBody): String

    @PUT
    suspend fun put(@Url Path: String, @Body body: RequestBody): String

    @DELETE
    suspend fun delete(
        @Url Path: String,
        @QueryMap map: Map<String, @JvmSuppressWildcards Any>,
    ): String

    @Multipart
    @POST
    suspend fun uploadFile(@Url path: String, @Part file: MultipartBody.Part): String


    @Streaming
    @GET
    fun download(@Url url: String): Call<ResponseBody>
}