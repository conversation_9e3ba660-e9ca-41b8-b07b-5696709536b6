package com.mrk.network.net

import android.net.Uri
import com.alibaba.fastjson.JSONObject
import com.jakewharton.retrofit2.adapter.kotlin.coroutines.CoroutineCallAdapterFactory
import com.mrk.network.MrkNetworkConfig
import okhttp3.MediaType
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.Retrofit
import java.io.File
import java.io.UnsupportedEncodingException
import java.net.URLEncoder


/**
 * author  : ww
 * desc    : 自定义网络请求
 * time    : 2021-12-21 16:27:46
 */
class MrkNetwork : BaseNetwork() {

    companion object {
        private val apiService: NetworkApi by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            val netOptions = MrkNetworkConfig.options
            if (netOptions.baseUrl.isNullOrEmpty()) {
                throw NullPointerException("baseUrl为空,请设置baseUrl!")
            }
            instance.getApi(NetworkApi::class.java, netOptions.baseUrl!!)
        }

        val instance: MrkNetwork by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            MrkNetwork()
        }
    }


    /**
     * 实现重写父类的setHttpClientBuilder方法，
     * 在这里可以添加拦截器，可以对 OkHttpClient.Builder 做任意操作
     */
    override fun setHttpClientBuilder(builder: OkHttpClient.Builder): OkHttpClient.Builder {
        return MrkNetworkConfig.options.okHttpClient
    }

    /**
     * 实现重写父类的setRetrofitBuilder方法，
     * 在这里可以对Retrofit.Builder做任意操作，比如添加GSON解析器，protobuf等
     */
    override fun setRetrofitBuilder(builder: Retrofit.Builder): Retrofit.Builder {
        return builder.apply {
            addConverterFactory(FastJsonConverterFactory.create())
            addCallAdapterFactory(CoroutineCallAdapterFactory())
        }
    }

    //为了以后 参数需要加密或者每个接口需要添加参数的问题,这里使用方法重新包装一次来请求
    suspend fun get(url: String, map: Map<String, Any>? = null): String {
        return if (map == null) apiService.get(getUrlFormat(url)) else apiService.get(
            getUrlFormat(
                url
            ), map
        )
    }

    //为了以后 参数需要加密或者每个接口需要添加参数的问题,这里使用方法重新包装一次来请求
    suspend fun post(url: String, map: Map<String, Any>? = null): String {
        return if (map != null) {
            val requestBody: RequestBody =
                RequestBody.create(
                    MediaType.parse("Content-Type, application/json"),
                    JSONObject(map).toString()
                )
            apiService.post(getUrlFormat(url), requestBody)
        } else {
            apiService.post(getUrlFormat(url))
        }

    }


    suspend fun post(url: String, jsonObject: JSONObject): String {
        val requestBody: RequestBody =
            RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                jsonObject.toString()
            )
        return apiService.post(getUrlFormat(url), requestBody)
    }

    suspend fun post(url: String, body: RequestBody): String {
        return apiService.post(getUrlFormat(url), body)
    }

    //为了以后 参数需要加密或者每个接口需要添加参数的问题,这里使用方法重新包装一次来请求
    suspend fun put(url: String, map: Map<String, Any>): String {
        val requestBody: RequestBody =
            RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                JSONObject(map).toString()
            )
        return apiService.put(getUrlFormat(url), requestBody)
    }

    //为了以后 参数需要加密或者每个接口需要添加参数的问题,这里使用方法重新包装一次来请求
    suspend fun delete(url: String, map: Map<String, Any>): String {
        return apiService.delete(getUrlFormat(url), map)
    }

    suspend fun uploadFile(url: String, file: File): String {
        //请求体
        val requestBody = RequestBody.create(MediaType.parse("multipart/form-data"), file)
        var fileName: String? = ""
        try {
            fileName = URLEncoder.encode(file.name, "UTF-8")
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        //单个文件请求
        val part = MultipartBody.Part.createFormData("file", fileName, requestBody)

        return apiService.uploadFile(getUrlFormat(url), part)
    }

    //下载
    fun download(url: String): Call<ResponseBody> {
        return apiService.download(url)
    }

    //动态切换baseUrl
    private fun getUrlFormat(url: String): String {
        val netOptions = MrkNetworkConfig.options
        if (netOptions.baseUrl.isNullOrEmpty()) {
            throw NullPointerException("baseUrl为空,请设置baseUrl!")
        }

        return if (url.startsWith("https://") || url.startsWith("http://")) {
            url
        } else {
            val baseUrl = netOptions.baseUrl!!
            val builder = Uri.parse(baseUrl).buildUpon()
            builder.path(url)
            builder.build().toString()
        }


    }

}