package com.mrk.network

import com.mrk.network.net.MrkNetOptions


/**
 * author  : ww
 * desc    : 网络请求配置
 * time    : 2022-02-16
 */
object MrkNetworkConfig {

    val options: MrkNetOptions
        get() {
            if (op == null) {
                op = MrkNetOptions.Builder().build()
            }
            return op!!
        }

    var op: MrkNetOptions? = null
    fun init(options: MrkNetOptions) {
        this.op = options
    }

}


