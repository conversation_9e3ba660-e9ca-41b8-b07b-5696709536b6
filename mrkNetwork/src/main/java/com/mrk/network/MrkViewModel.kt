package com.mrk.network

import androidx.lifecycle.ViewModel
import com.kunminx.architecture.ui.callback.UnPeekLiveData


open class MrkViewModel : ViewModel() {

    val loadingChange: UiLoadingChange by lazy { UiLoadingChange() }

    inner class UiLoadingChange {
        val showDialog by lazy { UnPeekLiveData<String>() }
        val dismissDialog by lazy { UnPeekLiveData<Boolean>() }
        val showToast by lazy { UnPeekLiveData<String>() }
    }

}




