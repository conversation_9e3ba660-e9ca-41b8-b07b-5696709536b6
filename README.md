## mrkBase 海外底层架构
## mrkBluetooth 设备架构
## mrkCommon 基础架构
## mrkDevice 封装了业务的设备架构
## mrkNetwork 网络请求架构
## mrkScale 体脂秤架构


## 连接状态/搜索日志tag BluetoothManager
### ota相关日志tag BaseDeviceOta
### 设备数据数据日志tag BaseDeviceFunction
### 设备搜索连接流程图 ![img.png](连接流程图.png)
### 运动设备数据交互流程图 ![img.png](蓝牙运动模式交互.png)


#### 更新记录

### com.mrk.common:mrkCommon:1.2.8 (2025年8月13日15:48:08)
## 因为埋点新增写入错误回调监听参数，导致调用方法默认监听错误的回调，导致无监听到跑步机发送启动指令成功回调，执行下一步发送控制速度坡度的指令，本次修改写入指令默认的监听为成功回调

### com.mrk.common:mrkCommon:1.2.7 (2025年8月1日09:43:10)
## vbConfig，设置日志拦截器改到build
## mrkBluetooth 增加日志
## mrkDevice 添加日志接口

### com.mrk.common:mrkCommon:1.2.6 (2025年6月13日11:53:15)
## permissionX 升级为1.8.1
## compileSdkVersion 与 targetSdkVersion 升级为34



### com.mrk.common:mrkCommon:1.2.5 (2025年4月11日17:51:39)
## DeviceControl，sendCommand里面的speed改为Number类型


### com.mrk.common:mrkCommon:1.2.4 (2025-02-08 11:01:48)
## vbLoadCircle，新增边框大小，边框颜色参数
## DeviceDetailsBean中增加字段isNewBindDevice
## FFF0Protocol 新增心率赋值


### com.mrk.common:mrkCommon:1.2.3 (2024-12-09 14:02:55)
## 体脂秤MrkScaleManager.addScaleListener() Lifecycle可不传。需自行调用removeScaleListener销毁方法

### com.mrk.common:mrkCommon:1.2.2 (2024-11-26 17:20:18)
## DeviceDetailsBean中增加字段equipImageGif

### com.mrk.common:mrkCommon:1.2.1 (2024年11月26日10:09:33)
## :DeviceDetailsBean中增加字段equipImageGif,版本改

### com.mrk.common:mrkCommon:1.2.0 (2024年11月19日10:09:33)
## :gradle 版本改为7.4

### com.mrk.common:mrkCommon:1.1.9 (2024年11月18日11:09:14)
## :"/app/device-user-rel/connectionDevice 新增firstBind字段"

### com.mrk.common:mrkCommon:1.1.8 (2024年10月23日11:59:25)
## :修复网络请求完成没有关闭dialog的问题

### com.mrk.common:mrkCommon:1.1.7 (2024年10月22日10:42:25)
## :Vlog库升级，修改网络请求异常监听位置

### com.mrk.common:mrkCommon:1.1.4-SNAPSHOT (2024年07月19日10:42:25)
## :解决三星手机 FRK S26C ota 失败

### com.mrk.common:mrkCommon:1.1.3 (2024年06月28日13:02:09)
## network模块开放添加证书方法（以适配MERACH，迁移AWS需要证书的问题）

### com.mrk.common:mrkCommon:1.1.2 (2024年06月04日20:02:09)
## 修复蓝牙连接后台没配置ota版本时候,获取ota错误问题.

### com.mrk.common:mrkCommon:1.1.1 (2024年05月21日16:19:29)
## 适配R11S

