package com.cc.control

import com.cc.control.BluetoothManager.client
import com.cc.control.BluetoothManager.getDeviceBean
import com.cc.control.BluetoothManager.sentryPushListener
import com.cc.control.DeviceFasciaGunFunction.Companion.STATUS_RUNNING
import com.cc.control.bean.DevicePropertyBean
import com.cc.control.bean.DeviceTrainBO
import com.cc.control.enums.PushLogLevel
import com.cc.control.enums.TreadmillStatus
import com.cc.control.protocol.DeviceConstants
import com.cc.control.protocol.DeviceConstants.D_SERVICE1826_2ADA
import com.cc.control.protocol.DeviceConvert
import com.cc.control.protocol.fff0Protocol
import com.cc.control.protocol.ftmsProtocol
import com.cc.control.protocol.mrkProtocol
import com.cc.control.protocol.string2UUID
import com.cc.control.protocol.vbContains
import com.cc.control.protocol.writeFTMSClear
import com.cc.control.protocol.writeFTMSControl
import com.cc.control.protocol.writeHeartRate
import com.inuker.bluetooth.library.Constants
import com.inuker.bluetooth.library.beacon.BeaconParser
import com.inuker.bluetooth.library.connect.response.BleNotifyResponse
import com.inuker.bluetooth.library.utils.ByteUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.UUID

/**
 * author  : cc
 * desc    : 蓝牙设备处理
 * time    : 2022/8/15
 */
abstract class BaseDeviceFunction(private var mDeviceType: String = "") {
    companion object {
        const val TAG = "BaseDeviceFunction"
    }

    /**
     * 获取数据、状态array
     */
    protected var dateArray: ArrayList<ByteArray> = ArrayList()
    private var writeIndex = 0  //循环发送index
    private var mHeartScope: Job? = null//心跳
    private var mDataScope: Job? = null//数据指令

    // 创建一个新的协程作用域
    protected var scope = CoroutineScope(Dispatchers.Default)

    /**
     * 游戏手柄
     */
    protected var mGameDataListener: ((key: Int, clickTime: Int) -> Unit)? = null

    /**
     * 柏群单车、跑步机需要先发连接指令,用于直播开启设备判断
     */
    protected var readyConnect = false

    /**
     * 刷新阻力速度坡度，避免自由训练控制过程中更新UI显示
     */
    open var isRefreshResistance = true

    /**
     * 通知数据回调，防止页面结束还收到数据并上报接口导致数据异常跟内存泄漏
     */
    open var isNotifyData = true

    /**
     * 记录设备解析完的数据
     */
    protected var notifyBean = DeviceTrainBO()

    /**
     *辅助通道数据
     */
    private var assistBean = DevicePropertyBean()

    /**
     *设备运行状态回调
     */
    protected var mStatusListener: ((Int) -> Unit)? = null

    /**
     * 连接状态回调
     */
    private var mConnectListener: ((deviceName: String, isConnect: Boolean) -> Unit)? = null

    /**
     * 数据回调
     */
    protected var mDataListener: ((DeviceTrainBO) -> Unit)? = null

    /**
     * 单位 设备类型0：公⾥，1：英⾥
     */
    protected var mUnitDistanceListener: ((Int) -> Unit)? = null

    /**
     * 是否需要注册订阅，因为多次订阅会收到数据频繁点
     */
    private var registerData = true

    /**
     * 蓝牙的回调
     */
    protected abstract fun onBluetoothNotify(
        service: UUID,
        character: UUID,
        parser: BeaconParser,
        dataSize: Int = 0,
    )

    /**
     * 目前智健跑步机速度跟坡度一条指令
     * speed 速度 resistance 阻力 slope 坡度
     * 有些设备支持坡度0所以需要标识区分
     */
    abstract fun setControl(
        speed: Int = 0,
        resistance: Int = 0,
        slope: Int = 0,
        isDelayed: Boolean = false,
        isSlope: Boolean = false,
    )

    /**
     *00H 正常模式（自由训练,数据不会清除）01H 倒计数 02H 倒计时 03H 超燃脂 (用于J001跳绳) 04H 游戏模式 游戏模式下屏蔽飞梭阻力调节功能
     */
    open fun setMode(model: Int = 0, targetNum: Int = 0, onSuccess: (() -> Unit) = {}) {}

    /**
     * 获取公英制单位
     */
    open fun getUnitDistance() {

    }

    /**
     * 设置设备数据 distance 距离(km)  kcal 消耗(k) time 时间(m) speed 速度(km/h) heart 心率(1Beats/Minute)
     */
    open fun setDeviceData(
        address: String = "",
        heart: String = "0",
        kcal: String = "0",
        distance: String = "0",
        speed: String = "0",
        time: String = "0",
    ) {
    }

    /**
     * 开始运动
     */
    open fun start() {
        getDeviceBean(mDeviceType).run {
            notifyBean.mac = this.address
            notifyBean.type = this.type
            notifyBean.name = this.name
            val map = mapOf("deviceType" to this.type, "bleName" to this.name)
            sentryPushListener?.invoke(PushLogLevel.INFO, "ble_sport_start", map.vbToJson())
        }
        writeHeart()
    }

    /**
     * 跑步机开始
     */
    open fun treadmillStart() {
        val map = mapOf("bleName" to notifyBean.name)
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_treadmill_start", map.vbToJson())
    }

    /**
     * 暂停
     */
    open fun treadmillPause() {
        val map = mapOf("bleName" to notifyBean.name)
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_treadmill_pause", map.vbToJson())
    }

    /**
     *继续
     */
    open fun treadmillContinue() {
        val map = mapOf("bleName" to notifyBean.name)
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_treadmill_continue", map.vbToJson())
    }

    /**
     *页面销毁数据清除等
     */
    abstract fun onDestroyWrite(): ByteArray?

    /**
     * 初始化设备
     */
    open fun initDevice() {
        val bean = getDeviceBean(mDeviceType)
        if (bean.assistProtocol > 0) {
            assistBean.protocol = bean.assistProtocol
            BluetoothManager.getService(assistBean)
        }
    }

    /**
     * 断开设备释放蓝牙请求数据资源
     */
    fun disConnectReset() {
        val map = mapOf(
            "deviceType" to mDeviceType,
            "bleName" to notifyBean.name,
            "dataScope" to mDataScope?.toString(),
            "isActive" to mDataScope?.isActive,
            "dataSize" to dateArray.size
        )
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_reset", map.vbToJson())
        registerData = true
        mDataScope?.cancel()
        mHeartScope?.cancel()
        readyConnect = false
        notifyBean.status = -1
    }

    /**
     * 写入指令
     */
    protected fun write(
        byteArray: ByteArray?,
        onError: ((Int) -> Unit)? = null,
        onSuccess: (() -> Unit)? = null,
    ) {
        if (byteArray == null) {
            return
        }
        getDeviceBean(mDeviceType).run {
            client.write(address, serviceUUID, writeUUID, byteArray) {
                if (it == Constants.REQUEST_SUCCESS) {
                    onSuccess?.invoke()
                } else {
                    onError?.invoke(it)
                }
                logI(
                    TAG,
                    "write: $serviceUUID $writeUUID ${ByteUtils.byteToString(byteArray)} $it"
                )
            }
        }
    }

    /**
     *读取指令
     */
    protected fun read(
        serviceUUID: UUID,
        characterUUId: UUID,
        onSuccess: ((ByteArray) -> Unit)?,
    ) {
        getDeviceBean(mDeviceType).run {
            client.read(address, serviceUUID, characterUUId) { code, data ->
                if (code == Constants.REQUEST_SUCCESS) {
                    onSuccess?.invoke(data)
                }
            }
            logI(TAG, "read $serviceUUID $characterUUId $address")
        }
    }

    /**
     *  设备请求数据指令
     */
    protected fun writeData() {
        val map = mapOf("dataScope" to mDataScope?.toString(), "isActive" to mDataScope?.isActive)
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_scope_writeData", map.vbToJson())
        if ((mDataScope == null || !mDataScope!!.isActive) && dateArray.size > 0) {
            mDataScope = scope.launch {
                while (isActive) { // 当协程处于活动状态时执行循环
                    write(dateArray[writeIndex % dateArray.size])
                    writeIndex++
                    // 暂停1秒钟
                    delay(500)
                }
            }
        }
    }

    /**
     * 设备心跳间隔20s一次
     */
    private fun writeHeart() {
        getDeviceBean(mDeviceType).run {
            if (hasHeartRate && (mHeartScope == null || !mHeartScope!!.isActive)) {
                mHeartScope = scope.launch {
                    while (isActive) { // 当协程处于活动状态时执行循环
                        client.write(
                            address,
                            string2UUID(DeviceConstants.D_SERVICE_MRK),
                            string2UUID(DeviceConstants.D_CHARACTER_HEART_MRK),
                            writeHeartRate()
                        ) {
                            logD(TAG, "deviceHeartRate: $it")
                        }

                        // 暂停1秒钟
                        delay(20000)
                    }
                }
            }

        }
    }

    /**
     * 数据接收处理
     */
    private val mNotifyRsp: BleNotifyResponse = object : BleNotifyResponse {
        override fun onResponse(code: Int) {
            logD(TAG, "mNotifyRsp：onResponse：$code")
        }

        override fun onNotify(service: UUID, character: UUID, value: ByteArray) {
            if (isNotifyData) {
                if (mDeviceType == DeviceConstants.D_GAME_KEYBOARD && value.isNotEmpty()) {
                    onBluetoothNotify(service, character, BeaconParser(value), value.size)
                } else if (service.toString() == DeviceConstants.D_SERVICE_DATA_HEART) {
                    onBluetoothNotify(service, character, BeaconParser(value), value.size)
                } else if (value.size >= 4) {
                    onBluetoothNotify(service, character, BeaconParser(value), value.size)
                }
            }
            notifyBean.originalData =
                "接收数据=$isNotifyData 时间戳${System.currentTimeMillis()} 服务值=$service 特征值=$character " + "数据=${
                    DeviceConvert.bytesToHexString(value)
                }"
            writeToFile(TAG, "onNotify: ${notifyBean.originalData}")
        }
    }

    /**
     * 发清除指令 设置1m清除响应时间
     */
    open fun clearData(onSuccess: (() -> Unit)? = null) {
        val deviceBean = getDeviceBean(mDeviceType)
        val map =
            mapOf(
                "deviceType" to mDeviceType,
                "bleName" to deviceBean.name,
                "bleCommunicationProtocol" to deviceBean.protocol,
                "dataScope" to mDataScope?.toString(),
                "isActive" to mDataScope?.isActive,
                "dataSize" to dateArray.size
            )
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_clear_data", map.vbToJson())
        if (deviceBean.protocol == DeviceConstants.D_SERVICE_TYPE_FTMS) {
            write(writeFTMSControl()) {
                write(writeFTMSClear()) {
                    //老版本华为单车需要断开连接
                    if (deviceBean.name.vbContains("MERACH-MR667") || deviceBean.name.vbContains("MERACH MR-667")) {
                        BluetoothManager.disConnect(deviceBean.address)
                    }
                    onSuccess?.invoke()
                }
            }
        }
        write(onDestroyWrite()) {
            CoroutineScope(Dispatchers.Default).launch {
                delay(1500)
                onSuccess?.invoke()
            }
        }
    }

    /**
     * 清除指令 0所有  读取Constants.REQUEST_READ  写请求Constants.REQUEST_WRITE，
     * 通知Constants.REQUEST_NOTIFY  读信号Constants.REQUEST_RSSI
     */
    private fun clearAllRequest(clearType: Int = Constants.REQUEST_WRITE) {
        client.clearRequest(getDeviceBean(mDeviceType).address, clearType)
    }

    /**
     *设置控制并延迟，旋钮的指令保护间隔>200ms isDelayed 是否延迟 自由训练实时数据采集，避免阻力冲突
     */
    private var setControlScope: Job? = null

    /**
     * 发送控制
     * @param writeData 数据
     * @param isDelayed 是否延迟，避免发送阻力等还没有响应立马收到阻力数据导致UI显示跳数据
     */
    protected fun setControl(writeData: ByteArray, isDelayed: Boolean = true) {
        val cmd = ByteUtils.byteToString(writeData)
        val map = mapOf("deviceType" to mDeviceType, "writeData" to cmd, "isDelayed" to isDelayed)
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_control", map.vbToJson())
        if (!isDelayed) {
            writeControl(writeData, cmd)
        } else if (getDeviceBean(mDeviceType).isConnect) {
            setControlScope?.cancel()
            setControlScope = CoroutineScope(Dispatchers.Default).launch {
                isRefreshResistance = false
                delay(300)
                clearAllRequest()
                writeControl(writeData, cmd)
                delay(600)
                isRefreshResistance = true
                cancel()
            }
        }
    }

    private fun writeControl(writeData: ByteArray, cmd: String) {
        write(
            writeData, onSuccess = {
                val map = mapOf(
                    "deviceType" to mDeviceType,
                    "writeData" to cmd,
                    "code" to Constants.REQUEST_SUCCESS
                )
                sentryPushListener?.invoke(
                    PushLogLevel.INFO,
                    "ble_control_callback",
                    map.vbToJson()
                )
            },
            onError = {
                val map = mapOf("deviceType" to mDeviceType, "writeData" to cmd, "code" to it)
                sentryPushListener?.invoke(
                    PushLogLevel.ERROR,
                    "ble_control_callback",
                    map.vbToJson()
                )
            })
    }
    /**
     * 设备重连 回调状态 1连接中 2连接成功 3连接失败
     */
//    fun connect(
//        isReconnect: Boolean = true,
//        connectListener: ((Int, Boolean) -> Unit)? = null,
//        mac: String
//    ) {
//        connectListener?.invoke(if (mac.isEmpty()) 3 else 1, isReconnect)
//        BluetoothManager.client.connect(mac, BleConfigOptions.connectOptions) { code, data ->
//            if (code == Constants.REQUEST_SUCCESS) {
//                BluetoothManager.client.registerConnectStatusListener(mac,
//                    BluetoothManager.mBleStatusListener)
//                //没有连接过，初始化设备信息
//                val name = if (records != null) {
//                    propertyBean.address = records.mac
//                    val heartService = string2UUID(DeviceConstants.D_SERVICE_MRK)
//                    val heartCharacter = string2UUID(DeviceConstants.D_CHARACTER_HEART_MRK)
//                    val heart = data.containsCharacter(heartService, heartCharacter)
//                    BluetoothManager.saveConnectMap(DevicePropertyBean(records.mac,
//                        records.productId,
//                        records.bluetoothName,
//                        data,
//                        heart))
//                    BluetoothManager.initProperty(records.productId, records.communicationProtocol)
//                    BluetoothManager.readOtaVersion(records.productId, records.versionEigenValue)
//                    records.bluetoothName
//                } else {
//                    val bean = BluetoothManager.getDeviceBean(propertyBean.address, false)
//                    bean.isConnect = false
//                    bean.name
//                }
//                if (name.vbContains("J003")) {
//                    MtuGattCallback(mac)
//                }
//                connectListener?.invoke(2, isReconnect)
//            } else {
//                connectListener?.invoke(3, isReconnect)
//            }
//            writeToFile(TAG, "bluetoothConnect:$code")
//        }
//    }

    /**
     * 获取当前设备是否运行
     */
    open fun isRunning(): Boolean {
        return if (mDeviceType == DeviceConstants.D_FASCIA_GUN) notifyBean.status == STATUS_RUNNING else notifyBean.status == TreadmillStatus.DEVICE_TREADMILL_RUNNING.num
    }

    /**
     *状态回调
     */
    open fun registerStatusListener(statusListener: (Int) -> Unit) {
        this.mStatusListener = statusListener
    }

    /**
     *数据回调回调
     * @param dataListener 数据回调
     * @param unitDistanceListener 设备单位回调 单位0：公⾥，1：英⾥
     */
    open fun registerDataListener(
        dataListener: ((DeviceTrainBO) -> Unit),
        unitDistanceListener: ((Int) -> Unit),
    ) {
        this.mDataListener = dataListener
        this.mUnitDistanceListener = unitDistanceListener
    }

    /**
     *手柄数据回调回调
     */

    open fun registerGameDataListener(mGameDataListener: ((Int, Int) -> Unit)) {
        this.mGameDataListener = mGameDataListener
    }

    /**
     * 订阅数据通道，华为服务订阅控制通知
     */
    open fun registerNotify() {
        val bean = getDeviceBean(mDeviceType)
        val map =
            mapOf("deviceType" to bean.type, "bleName" to bean.name, "registerData" to registerData)
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_register_notify", map.vbToJson())
        if (registerData) {
            bean.run {
                client.notify(address, serviceUUID, notifyUUID, mNotifyRsp)
                //华为通道订阅数据通知
                if (serviceUUID.toString().contains("1826")) {
                    client.notify(address, serviceUUID, string2UUID(D_SERVICE1826_2ADA), mNotifyRsp)
                }
                if (protocol != assistProtocol && assistProtocol > 0) {
                    client.notify(
                        address,
                        assistBean.serviceUUID,
                        assistBean.notifyUUID,
                        assistNotify
                    )
                }
                logI(TAG, "notifyRegister $serviceUUID $notifyUUID $address")
            }
            registerData = false
            isNotifyData = true
        }
    }

    /**
     * 辅助通道数据
     */
    private val assistNotify: BleNotifyResponse = object : BleNotifyResponse {
        override fun onResponse(code: Int) {
            logD(TAG, "assistResponse：$code")
        }

        override fun onNotify(service: UUID, character: UUID, value: ByteArray) {
            val notifyBean = DeviceTrainBO(dataType = 3)
            if (value.size >= 4) {
                val parser = BeaconParser(value)
                when (assistBean.protocol) {
                    DeviceConstants.D_SERVICE_TYPE_MRK -> {
                        mrkProtocol(notifyBean, parser, value.size)
                    }

                    DeviceConstants.D_SERVICE_TYPE_FTMS -> {
                        val name = getDeviceBean(mDeviceType).name
                        ftmsProtocol(notifyBean, name, mDeviceType, parser)
                    }

                    DeviceConstants.D_SERVICE_TYPE_ZJ, DeviceConstants.D_SERVICE_TYPE_BQ -> {
                        fff0Protocol(notifyBean, mDeviceType, parser, value.size) { _, _ -> }
                    }
                }
                notifyBean.originalData =
                    "接收数据=$isNotifyData 时间戳${System.currentTimeMillis()} 服务值=$service 特征值=$character " + "数据=${
                        DeviceConvert.bytesToHexString(value)
                    }"
                writeToFile(TAG, "assistResponse: ${notifyBean.originalData}")
                mDataListener?.invoke(notifyBean)
            }
        }
    }

    /**
     * 取消订阅
     */
    open fun unRegisterNotify() {
        val bean = getDeviceBean(mDeviceType)
        val map =
            mapOf("deviceType" to bean.type, "bleName" to bean.name)
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_unRegister_notify", map.vbToJson())
        bean.run {
            if (serviceUUID != null) {
                client.unnotify(address, serviceUUID, notifyUUID) {}
            }
            if (protocol != assistProtocol && assistProtocol > 0 && assistBean.serviceUUID != null) {
                client.unnotify(address, assistBean.serviceUUID, assistBean.notifyUUID) {}
            }
        }
        registerData = true
        isNotifyData = false
    }

    /**
     * 结束运动
     */
    open fun end() {
        val map = mapOf(
            "deviceType" to mDeviceType,
            "bleName" to notifyBean.name
        )
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_end", map.vbToJson())
        registerData = true
        isNotifyData = false
        scope.cancel()
        mDataListener = null
        mStatusListener = null
        mConnectListener = null
    }
}