package com.cc.control.ota

import android.net.Uri
import android.text.TextUtils
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.cc.control.BluetoothManager
import com.cc.control.bean.DevicePropertyBean
import com.cc.control.logD
import com.cc.control.protocol.CRC16
import com.cc.control.protocol.DeviceConstants
import com.cc.control.protocol.DeviceConvert.bytesToHexString
import com.cc.control.protocol.string2UUID
import com.inuker.bluetooth.library.Code.REQUEST_SUCCESS
import com.inuker.bluetooth.library.Constants
import com.inuker.bluetooth.library.connect.response.BleNotifyResponse
import com.inuker.bluetooth.library.utils.ByteUtils
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.util.*
import kotlin.math.floor

/**
 * <AUTHOR> cc
 * @Date        : on 2022-02-15 13:41.
 * @Description :ota 基类
 */
abstract class BaseDeviceOta : DefaultLifecycleObserver {
    protected val TAG = "BaseDeviceOta"

    protected lateinit var devicePropertyBean: DevicePropertyBean

    /**
     *  ota回调
     */
    protected var deviceOtaListener: OtaListener? = null

    /**
     * fileName 文件路径
     */
    abstract fun startUpdate(filePath: String)

    /**
     * 结束标识
     */
    protected var isFinish = false

    /**
     * 数据回调
     */
    protected abstract fun onBluetoothNotify(service: UUID?, character: UUID?, value: ByteArray)

    /**
     * DFU
     */
    protected var mActivity: AppCompatActivity? = null
    protected var dfuUri: Uri? = null

    /**
     * 创建对象
     */
    open fun create(
        deviceType: String = "",
        otaType: Int,
        activity: AppCompatActivity? = null,
        uri: Uri? = null,
        otaListener: OtaListener? = null,
    ) {
        mActivity = activity
        dfuUri = uri
        deviceOtaListener = otaListener
        devicePropertyBean = BluetoothManager.getDeviceBean(deviceType)
        initOta(otaType)
        devicePropertyBean.run {
            if (otaNotify != null) {
                BluetoothManager.client.notify(address, otaService, otaNotify,
                    object : BleNotifyResponse {
                        override fun onResponse(code: Int) {}
                        override fun onNotify(service: UUID?, character: UUID?, value: ByteArray) {
                            if (value.isNotEmpty()) {
                                onBluetoothNotify(service, character, value)
                            }
                        }
                    })
            }
        }

    }
//   private  otaNotify
    /**
     *初始化ota 相关信息
     * @param  mOtaType 类型
     */
    private fun initOta(mOtaType: Int) {
        BluetoothManager.getDeviceBean(devicePropertyBean.type).run {
            when (mOtaType) {
                DeviceConstants.D_OTA_BT -> {
                    otaService = string2UUID(DeviceConstants.D_SERVICE_OTA_BT)
                    otaControl = string2UUID(DeviceConstants.D_CHARACTER_OTA_BT1)
                    otaWrite = string2UUID(DeviceConstants.D_CHARACTER_OTA_BT2)
                }
                DeviceConstants.D_OTA_TLW -> {
                    otaService = string2UUID(DeviceConstants.D_SERVICE_OTA_TLW)
                    otaWrite = string2UUID(DeviceConstants.D_CHARACTER_OTA_TLW)
                }
                DeviceConstants.D_OTA_XXY -> {
                    otaService = string2UUID(DeviceConstants.D_SERVICE_OTA_XXY)
                    otaWrite = string2UUID(DeviceConstants.D_CHARACTER_OTA_XXY)
                }
                DeviceConstants.D_OTA_FRK -> {
                    otaService = string2UUID(DeviceConstants.D_SERVICE_OTA_FRK)
                    otaWrite = string2UUID(DeviceConstants.D_CHARACTER_OTA_FRK)
                    otaNotify = string2UUID(DeviceConstants.D_NOTIFY_OTA_FRK)
                }
                DeviceConstants.D_OTA_LSW -> {
                    otaService = string2UUID(DeviceConstants.D_SERVICE_OTA_LSW)
                    otaWrite = string2UUID(DeviceConstants.D_CHARACTER_OTA_LSW)
                    otaControl = string2UUID(DeviceConstants.D_CONTROL_OTA_LSW)
                    otaNotify = string2UUID(DeviceConstants.D_CONTROL_OTA_LSW)
                }
            }
        }
    }

    /**
     * 写入数据 LSW 需要用来两个特征值操作
     */
    protected fun write(
        byteArray: ByteArray,
        control: Boolean = false,
        onSuccess: (() -> Unit)? = null,
    ) {
        devicePropertyBean.run {
            val uuid = if (control) otaControl else otaWrite // true 7000
            BluetoothManager.client.write(address, otaService, uuid, byteArray) {
                val sb = StringBuffer()
                sb.append("mtu:$mtu")
                sb.append("\t")
                sb.append(bytesToHexString(byteArray))
                sb.append("\t")
                sb.append("地址:$address")
                sb.append("\t")
                sb.append("服务:" + otaService.toString())
                sb.append("\t")
                sb.append("特征:$uuid")
                val writeStatus = if (it == Constants.REQUEST_SUCCESS) {
                    onSuccess?.invoke()
                    "OTA写入成功:"
                } else {
                    "OTA写入失败:"
                }
                sb.append(writeStatus)
                logD(TAG, "write: $sb")
            }
        }
    }

    /**
     * 因为部分设备不支持 writeNoRsp 所以目前暂时统一write后续优化
     */
    protected fun writeOta(
        value: ByteArray,
        totalSize: Int,
        position: Int,
        onSuccess: (() -> Unit)? = null,
    ) {
        devicePropertyBean.run {
            BluetoothManager.client.write(address, otaService, otaWrite, value) { code ->
                if (code == REQUEST_SUCCESS) {
                    val progress = position + 1
                    logD(TAG, "writeNoRsp:总:$totalSize pro:$progress ${bytesToHexString(value)}")
                    deviceOtaListener?.onProgress(floor(progress * 1.0 / totalSize * 100).toInt())
                    onSuccess?.invoke()
                } else {
                    deviceOtaListener?.onError()
                    logD(TAG, "writeNoRsp 失败:${bytesToHexString(value)} ")
                }
            }
        }
    }

    protected fun writeOtaNoRsp(
        value: ByteArray,
        totalSize: Int,
        position: Int,
        onSuccess: (() -> Unit)? = null,
    ) {
        devicePropertyBean.run {
            BluetoothManager.client.writeNoRsp(address, otaService, otaWrite, value) { code ->
                if (code == REQUEST_SUCCESS) {
                    val progress = position + 1
                    logD(TAG, "writeNoRsp:总:$totalSize pro:$progress ${bytesToHexString(value)}")
                    deviceOtaListener?.onProgress(floor(progress * 1.0 / totalSize * 100).toInt())
                    onSuccess?.invoke()
                } else {
                    deviceOtaListener?.onError()
                    logD(TAG, "writeNoRsp 失败:${bytesToHexString(value)} ")
                }
            }
        }
    }

    /**
     *读取
     */
    protected fun read(onSuccess: ((ByteArray) -> Unit)? = null) {
        devicePropertyBean.run {
            BluetoothManager.client.read(address, otaService, otaWrite) { code, data ->
                if (code == Constants.REQUEST_SUCCESS) {
                    onSuccess?.invoke(data)
                    logD(TAG, "read: ${bytesToHexString(data)}$otaService $otaWrite")
                }
            }
        }

    }

    /**
     *  关闭刷新通知
     */
    fun resetUpdate() {
        devicePropertyBean.run {
            if (otaNotify != null)
                BluetoothManager.client.unnotify(address, otaService, otaNotify) {}
        }
        deviceOtaListener = null
        isFinish = true
    }

    /**
     * 释放回调监听
     */
    protected open fun unregisterListener() {

    }

    /**
     * 结束指令
     */
    protected fun writeOtaFinish(index: Int): ByteArray {
        return ByteUtils.stringToBytes("02ff" + CRC16.stringTransposition(index) + CRC16.stringTransposition(
            index.inv() and 0xffff))
    }

    override fun onDestroy(owner: LifecycleOwner) {
        resetUpdate()
        unregisterListener()
        super.onDestroy(owner)
    }

    protected fun String.readFileToByteArray(): ByteArray {
        var bytes = ByteArray(0)
        try {
            val fis = FileInputStream(this)
            val max = fis.available()
            bytes = ByteArray(max)
            fis.read(bytes)
            fis.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return bytes
    }

    /**
     * Indicates if this file represents colorlist file on the underlying file system.
     *
     *  文件路径
     * @return 是否存在文件
     */
    protected fun String.isFileExist(): Boolean {
        if (TextUtils.isEmpty(this)) {
            return false
        }
        val file = File(this)
        return file.exists() && file.isFile
    }

}