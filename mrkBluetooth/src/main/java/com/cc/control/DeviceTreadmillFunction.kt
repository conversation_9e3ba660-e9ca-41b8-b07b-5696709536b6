package com.cc.control

import android.util.Log
import com.cc.control.BluetoothManager.sentryPushListener
import com.cc.control.enums.PushLogLevel
import com.cc.control.enums.TreadmillStatus
import com.cc.control.protocol.DeviceConstants
import com.cc.control.protocol.DeviceConvert
import com.cc.control.protocol.readZJModelId
import com.cc.control.protocol.writeTreadmillClear
import com.cc.control.protocol.writeTreadmillControl
import com.cc.control.protocol.writeTreadmillData
import com.cc.control.protocol.writeTreadmillReady
import com.cc.control.protocol.writeTreadmillStart
import com.cc.control.protocol.writeTreadmillStop
import com.cc.control.protocol.writeZJTreadmillUnit
import com.inuker.bluetooth.library.beacon.BeaconParser
import java.util.UUID

/**
 * Author      : cc
 * Date        : on 2022-02-18 18:38.
 * Description : 智健跑步机 x5、x3、x1、马克龙
 */
open class DeviceTreadmillFunction(device: String) : BaseDeviceFunction(device) {
    /**
     * 先获取跑步机的状态，自由训练、直播录播、视频根据当前状态处理，避免x5、T05部分设备不主动获取而无法开始
     */
    override fun start() {
        super.start()
        if (dateArray.isEmpty()) {
            dateArray.add(writeTreadmillData())
        }
        writeData()
    }

    /**
     * 兼容跑步机未连接状态教案开始需要先发送连接再发送速度
     */
    private fun onWriteStart(onSuccessCallback: (() -> Unit)? = null) {
        if (dateArray.isEmpty()) {
            dateArray.add(writeTreadmillData())
        }
        if (readyConnect) {
            writeData()
            onSuccessCallback?.invoke()
        } else {
            write(readZJModelId())
            if (isRunning()) {
                write(writeTreadmillReady()) {
                    readyConnect = true
                    writeData()
                    onSuccessCallback?.invoke()
                }
            } else {
                write(writeTreadmillStart()) {
                    write(writeTreadmillReady()) {
                        readyConnect = true
                        writeData()
                        onSuccessCallback?.invoke()
                    }
                }
            }
        }
    }


    /**
     * 智健协议单位0.1所以跑步机速度需要*10
     */
    override fun setControl(
        speed: Int,
        resistance: Int,
        slope: Int,
        isDelayed: Boolean,
        isSlope: Boolean,
    ) {
        onWriteStart {
            setControl(writeTreadmillControl(speed, slope), isDelayed)
        }
        writeToFile(TAG, "setControl $speed $slope $readyConnect")
    }

    override fun getUnitDistance() {
        write(readZJModelId())
        write(writeZJTreadmillUnit())
        val map =
            mapOf(
                "deviceType" to DeviceConstants.D_TREADMILL,
                "bleCommunicationProtocol" to DeviceConstants.D_SERVICE_TYPE_ZJ
            )
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_getUnit", map.vbToJson())
    }

    /**
     * 暂停发开始
     */
    override fun treadmillStart() {
        super.treadmillStart()
        onWriteStart {
            Log.e("isRunning()", isRunning().toString())
            if (!isRunning()) {
                write(writeTreadmillStart())
            }
        }
    }

    override fun treadmillPause() {
        super.treadmillPause()
        if (notifyBean.status == TreadmillStatus.DEVICE_TREADMILL_RUNNING.num) {
            write(writeTreadmillStop())
        }
    }

    /**
     * 待机就绪
     */
    override fun treadmillContinue() {
        super.treadmillContinue()
        onWriteStart {
            if (!isRunning()) {
                write(writeTreadmillStart())
            }
        }

    }

    override fun onBluetoothNotify(
        service: UUID,
        character: UUID,
        parser: BeaconParser,
        dataSize: Int,
    ) {
        parser.readByte()//标识位
        val code = parser.readByte()
        if (code == 0x51) {
            notifyBean.run {
                val deviceStatus = parser.readByte()
                if (deviceStatus != status) {
                    status = deviceStatus
                    mStatusListener?.invoke(deviceStatus)
                    //如果设备暂停了 则手动把坡度 速度设置为0
                    if (deviceStatus == TreadmillStatus.DEVICE_TREADMILL_PAUSE.num || deviceStatus == TreadmillStatus.DEVICE_TREADMILL_AWAIT.num) {
                        speed = 0.0f
                        gradient = 0
                    }
                    mDataListener?.invoke(this)
                }
                status = deviceStatus
                //运行中并且长度符合防止脏数据
                if (deviceStatus != TreadmillStatus.DEVICE_TREADMILL_RUNNING.num || dataSize != 17) return
                speed = (parser.readByte() / 10.0).toFloat() //当前速度
                val data = parser.readByte()//当前坡度
                gradient = if (data > 200) {
                    data - 0xff - 1
                } else {
                    data
                }
                deviceTime = parser.readShort().toLong() //时间
                var realDistance = parser.readShort() //距离
                if (realDistance and 0x8000 == 0x8000) {
                    realDistance = (realDistance and 0x7FFF) * 10
                }
                distance = realDistance
                energy = (parser.readShort() / 10.0f) // 10.0//热量
                count = parser.readShort() //步数
                val rat = parser.readByte()
                this.deviceRate = rat.coerceAtMost(200) //当前心率
                mDataListener?.invoke(this)
            }
        } else if (code == 0x50) {
            //获取设备坡度参数（0x50-0x03）（必选）
            val status = parser.readByte()//0x03
            if (status != 0x03) {
                return
            }
            parser.readByte()//最⾼坡度
            parser.readByte()//最低坡度
            var unit = DeviceConvert.getBit(parser.readByte(), 0) //配置
            if (unit != -1) {
                unit = if (unit == 1) 2 else 1
            }
            // 因为设备返回单位是 0：公⾥，1：英⾥ 所这里做一次转换转换成服务器需要的1-公制，2-英制
            notifyBean.unitDistance = unit
            mUnitDistanceListener?.invoke(unit)
        }
    }

    override fun onDestroyWrite(): ByteArray {
        return writeTreadmillClear()
    }
}