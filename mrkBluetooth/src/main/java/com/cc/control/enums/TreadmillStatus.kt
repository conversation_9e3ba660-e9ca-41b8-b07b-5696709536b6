package com.cc.control.enums

/**
 * 跑步机状态枚举
 */
enum class TreadmillStatus(val num: Int) {
    DEVICE_TREADMILL_STOP(0x01),//停机
    DEVICE_TREADMILL_RUNNING(0x03),//运行中
    DEVICE_TREADMILL_PAUSE(0x0A),//暂停
    DEVICE_TREADMILL_AWAIT(0x00),//待机
    DEVICE_TREADMILL_LAUNCHING(0x02),//启动中
    DEVICE_TREADMILL_COUNTDOWN(0x04),//启动中
    DEVICE_TREADMILL_MALFUNCTION(0x05),//故障，部分跑步机安全锁脱落返回
    DEVICE_TREADMILL_DISABLE(0x06),//故障，部分跑步机安全锁脱落返回
}

