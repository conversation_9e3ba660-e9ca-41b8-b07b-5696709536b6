package com.cc.control.enums

/**
 * the SentryLevel for Logs
 */
sealed class PushLogLevel(val level: Int) {
    object TRACE : PushLogLevel(1)
    object DEBUG : PushLogLevel(5)
    object INFO : PushLogLevel(9)
    object WARN : PushLogLevel(13)
    object ERROR : PushLogLevel(17)
    object FATAL : PushLogLevel(21)

    fun getLogLevelValue(): Int {
        return level
    }

    companion object {
        fun fromValue(level: Int): PushLogLevel {
            return when (level) {
                1 -> TRACE
                5 -> DEBUG
                9 -> INFO
                13 -> WARN
                17 -> ERROR
                21 -> FATAL
                else -> INFO
            }
        }
    }
}