package com.cc.control.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR> cc
 * @Date        : on 2022-02-15 14:05.
 * @Description :设备状态监听
 */
@Parcelize
data class DeviceConnectBean(
    var address: String = "",//连接地址
    var type: String = "",//心率带用到了
    var name: String = "",//设备名称如果接口获取mac为空需要用蓝牙名称判断是否同一个设备
    var isConnect: Boolean = false,//连接状态 true  requestDevice 成功 才为true
    var characteristic: List<CharacteristicBean> = listOf(),//请求连接接口需要 所有特征值数据
    var uniqueModelIdentify: List<CharacteristicBean>? = listOf(),//请求连接接口需要 唯一确定的特征值数据
) : Parcelable
