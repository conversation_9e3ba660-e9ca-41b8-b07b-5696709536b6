package com.cc.control

import android.annotation.SuppressLint
import android.app.Application
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothGattCharacteristic.PROPERTY_INDICATE
import android.bluetooth.BluetoothGattCharacteristic.PROPERTY_NOTIFY
import android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE
import android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.cc.control.bean.CharacteristicBean
import com.cc.control.bean.DevicePropertyBean
import com.cc.control.enums.PushLogLevel
import com.cc.control.ota.MtuGattCallback
import com.cc.control.protocol.BleConfigOptions.bleSearchRequest
import com.cc.control.protocol.BleConfigOptions.connectOptions
import com.cc.control.protocol.DeviceConstants
import com.cc.control.protocol.DeviceConvert
import com.cc.control.protocol.getUUIdFromList
import com.cc.control.protocol.string2UUID
import com.cc.control.protocol.vbContains
import com.inuker.bluetooth.library.BluetoothClient
import com.inuker.bluetooth.library.Code
import com.inuker.bluetooth.library.Constants
import com.inuker.bluetooth.library.connect.listener.BleConnectStatusListener
import com.inuker.bluetooth.library.connect.listener.BluetoothStateListener
import com.inuker.bluetooth.library.search.SearchResult
import com.inuker.bluetooth.library.search.response.SearchResponse
import com.inuker.bluetooth.library.utils.BluetoothUtils
import java.util.UUID


/**
 * cc
 * 蓝牙控制类
 */
object BluetoothManager {
    private var mClient: BluetoothClient? = null
    const val logTag = "BluetoothManager"

    private var onBluetoothStatusListener: ((Boolean) -> Unit)? = null

    /**
     * 设备连接详情集合,连接中、历史连接记录
     */
    private val deviceConnectMap = HashMap<String, DevicePropertyBean>()

    lateinit var mApplication: Application

    /**
     * 所有设备的断开回调
     */
    private var disConnectListener: ((mac: String) -> Unit)? = null

    /**
     * 需要单独监听断开的mac
     */
    private var disConnectCurrentMac = ""

    /**
     * 监听某一个设备断开回调
     */
    private var disConnectCurrentListener: ((mac: String) -> Unit)? = null

    /**
     * sentry 埋点
     * 具体参考 https://alidocs.dingtalk.com/i/nodes/dpYLaezmVNZE43mzSZM0rzGZVrMqPxX6?utm_scene=person_space
     * @param logLevel SentryLogLevel
     * @param msssage 标题用于后续筛选日志
     * @param extra 参数json 字符串
     */
    var sentryPushListener: ((logLevel: PushLogLevel, message: String, extra: String) -> Unit)? =
        null

    /**
     * 蓝牙唯一操作对象
     */
    val client: BluetoothClient
        get() {
            if (mClient == null) {
                synchronized(BluetoothManager::class.java) {
                    if (mClient == null) {
                        mClient = BluetoothClient(mApplication)
                    }
                }
            }
            return mClient!!
        }

    /**
     * 蓝牙状态监听
     */
    private val mBleStatusListener by lazy {
        BleStatusListener()
    }

    /**
     * 是否打印日志
     */
    var isShowLog = false

    /**
     * 必须先初始化不然无法引用context
     */
    fun initDeviceOption(app: Application, showLog: Boolean = false, logPath: String = "log") {
        mApplication = app
        isShowLog = showLog
        logPathName = logPath
    }

    /**
     * 系统蓝牙开关监听
     */
    private var lastBoolean = false
    fun registerBluetoothStateListener(isOpen: ((Boolean) -> Unit)? = null) {
        lastBoolean = isBluetoothOpened()
        client.registerBluetoothStateListener(object : BluetoothStateListener() {
            override fun onBluetoothStateChanged(openOrClosed: Boolean) {
                //过滤短时间大量同样的值
                if (lastBoolean != openOrClosed) {
                    sentryPushListener?.invoke(
                        PushLogLevel.INFO,
                        "ble_system_switch",
                        mapOf("isOpenBle" to openOrClosed).vbToJson()
                    )
                    lastBoolean = !lastBoolean
                    onBluetoothStatusListener?.invoke(openOrClosed)
                    onBluetoothStatusListener = null
                    isOpen?.invoke(openOrClosed)
                }
            }
        })
    }

    /**
     * sentry 埋点回调监听
     */
    fun registerSentryLogListener(pushListener: (logLevel: PushLogLevel, message: String, extra: String) -> Unit) {
        sentryPushListener = pushListener
    }

    /**
     * sentry 取消订阅回调
     */
     fun unRegisterSentryLogListener() {
        sentryPushListener = null
    }

    fun registerDisConnectListener(isDisConnect: (mac: String) -> Unit) {
        disConnectListener = isDisConnect
    }

    /**
     *连接设备
     * @param type 设备大类
     * @param name 蓝牙名称
     */
    fun connect(
        mac: String,
        type: String,
        name: String,
        connectListener: (isConnect: Boolean, List<CharacteristicBean>) -> Unit,
    ) {
        val map =
            mapOf("deviceType" to type, "bleName" to name, "bleMac" to mac)
        sentryPushListener?.invoke(
            PushLogLevel.INFO,
            "ble_connect",
            map.vbToJson()
        )
        if (mac.isEmpty()) {
            startSearch(listener = { deviceName, address ->
                if (address.isNotEmpty()) {
                    bluetoothConnect(address, deviceName, type, connectListener)
                } else {
                    connectListener.invoke(false, listOf())
                }
            }, deviceName = name)
        } else {
            bluetoothConnect(mac, name, type, connectListener)
        }
    }

    /**getExternalFilesDir
     * 连接健康设备不关闭重连
     */
    private fun bluetoothConnect(
        mac: String,
        name: String,
        type: String,
        connectListener: (isConnect: Boolean, List<CharacteristicBean>) -> Unit,
    ) {
        client.connect(mac, connectOptions) { code, data ->
            //心率带可以直接获取设备信息
            if (code == Constants.REQUEST_SUCCESS) {
                val heartService = string2UUID(DeviceConstants.D_SERVICE_MRK)
                val heartCharacter = string2UUID(DeviceConstants.D_CHARACTER_HEART_MRK)
                val heart = data.containsCharacter(heartService, heartCharacter)
                deviceConnectMap[type] = DevicePropertyBean(mac, type, name, data, heart)
                if (name.vbContains("J003")) {
                    MtuGattCallback(mac)
                }
                //部分设备获取设备模块信息才能区分
                val characterBeanList = data.getUUIdFromList()
                if (characterBeanList.isEmpty()) {
                    connectListener.invoke(false, listOf())
                } else {
                    var countValue = 0 //read完所有设备信息后进行通知
                    characterBeanList.forEach {
                        if (it.isContains == true) {
                            if (it.serviceUUID!!.isNotEmpty() && it.characteristicUUID!!.isNotEmpty()) {
                                client.read(
                                    mac,
                                    string2UUID(it.serviceUUID!!),
                                    string2UUID(it.characteristicUUID!!)
                                ) { _: Int, data: ByteArray ->

                                    val value = DeviceConvert.bytesToAsciiString(data)
                                    it.characteristicValue = value
                                    countValue++
                                    if (countValue == characterBeanList.size) {
                                        connectListener.invoke(true, characterBeanList)
                                    }
                                }
                            }
                        } else {
                            countValue++
                            if (countValue == characterBeanList.size) {
                                connectListener.invoke(true, characterBeanList)
                            }
                        }
                    }
                }
                if (type == DeviceConstants.D_HEART) {
                    initProperty(DeviceConstants.D_HEART)
                }
                client.registerConnectStatusListener(mac, mBleStatusListener)
                val map =
                    mapOf("deviceType" to type, "bleName" to name, "bleMac" to mac, "code" to code)
                sentryPushListener?.invoke(
                    PushLogLevel.INFO,
                    "ble_connect_callback",
                    map.vbToJson()
                )
            } else {
                connectListener.invoke(false, listOf())
                val map =
                    mapOf("deviceType" to type, "bleName" to name, "bleMac" to mac, "code" to code)
                sentryPushListener?.invoke(
                    PushLogLevel.ERROR,
                    "ble_connect_callback",
                    map.vbToJson()
                )
            }
            writeToFile(logTag, "bluetoothConnect:$code  ${Code.toString(code)}")
        }
    }

    /**
     * 初始化设备配置
     * @param mProtocol 协议类型
     * @param mAssistProtocol  目前游戏使用辅助协议类型
     */
    fun initProperty(deviceType: String, mProtocol: Int = 1, mAssistProtocol: Int = 0) {
        getDeviceBean(deviceType).run {
            protocol = mProtocol
            assistProtocol = mAssistProtocol
            getService(this)
            isConnect = true
            deviceConnectMap[type] = this//因为首次连接map 里面没有所以重新初始化
            sentryPushListener?.invoke(
                PushLogLevel.INFO,
                "ble_initProperty",
                mapOf("deviceType" to deviceType, "bleCommunicationProtocol" to protocol).vbToJson()
            )
            writeToFile(logTag, "数据通道 $type $mProtocol")
        }
    }

    /**
     * 根据协议获取配置参数
     */
    fun getService(bean: DevicePropertyBean) {
        bean.run {
            serviceUUID = when (protocol) {
                DeviceConstants.D_SERVICE_TYPE_MRK -> {
                    notifyUUID = string2UUID(DeviceConstants.D_CHARACTER_DATA_MRK)
                    writeUUID = notifyUUID
                    string2UUID(DeviceConstants.D_SERVICE_MRK)
                }

                DeviceConstants.D_SERVICE_TYPE_FTMS -> {
                    notifyUUID = getHWNotify(type)
                    writeUUID = string2UUID(DeviceConstants.D_SERVICE1826_2AD9)
                    string2UUID(DeviceConstants.D_SERVICE1826)
                }

                DeviceConstants.D_SERVICE_TYPE_ZJ -> {
                    string2UUID(DeviceConstants.D_SERVICE_FFFO)
                }

                DeviceConstants.D_SERVICE_TYPE_FASCIA -> {
                    string2UUID(DeviceConstants.D_SERVICE_FFFO)
                }

                DeviceConstants.D_SERVICE_TYPE_BQ -> {
                    val service = bleProfile?.getService(string2UUID(DeviceConstants.D_SERVICE_BQ))
                    if (service == null) {
                        string2UUID(DeviceConstants.D_SERVICE_FFFO)
                    } else {
                        writeUUID = string2UUID(DeviceConstants.D_CHARACTER_BQ)
                        notifyUUID = writeUUID
                        service.uuid
                    }
                }

                DeviceConstants.D_SERVICE_TYPE_OTHER -> {
                    val service =
                        bleProfile?.getService(string2UUID(DeviceConstants.D_SERVICE_FFFO))
                    if (service == null) {
                        protocol = 2
                        notifyUUID = getHWNotify(type)
                        writeUUID = string2UUID(DeviceConstants.D_SERVICE1826_2AD9)
                        string2UUID(DeviceConstants.D_SERVICE1826)
                    } else {
                        protocol = 3
                        service.uuid
                    }
                }

                DeviceConstants.D_SERVICE_TYPE_HEART -> {
                    notifyUUID = string2UUID(DeviceConstants.D_CHARACTER_DATA_HEART)
                    string2UUID(DeviceConstants.D_SERVICE_DATA_HEART)
                }

                DeviceConstants.D_SERVICE_TYPE_GAME -> {
                    notifyUUID = string2UUID(DeviceConstants.D_CHARACTER_DATA_MRK)
                    writeUUID = notifyUUID
                    string2UUID(DeviceConstants.D_SERVICE_MRK)
                }

                DeviceConstants.D_SERVICE_TYPE_KEYBOARD -> {
                    var service = string2UUID(DeviceConstants.D_SERVICE_MRK)
                    bleProfile?.services?.forEach { it ->
                        if (it.uuid.toString().contains("AE30", true)) {
                            it.characters.forEach { characters ->
                                if (characters.uuid.toString().contains("AE02", true)) {
                                    service = it.uuid
                                    notifyUUID = characters.uuid
                                    writeUUID = characters.uuid
                                }
                            }
                        }
                    }
                    service
                }

                else -> string2UUID(DeviceConstants.D_SERVICE_MRK)
            }
            //主要FFF0
            if (writeUUID == null && DeviceConstants.D_SERVICE_TYPE_HEART != protocol) bleProfile?.getService(
                serviceUUID
            )?.characters?.forEach {
                if (it.property and PROPERTY_WRITE_NO_RESPONSE > 0 || it.property and PROPERTY_WRITE > 0) {
                    writeUUID = it.uuid
                } else if (it.property and PROPERTY_NOTIFY > 0 || it.property and PROPERTY_INDICATE > 0) {
                    notifyUUID = it.uuid
                }
            }
        }
    }

    /**
     * 获取设备硬件跟软件版本
     * @param otaListener 返回硬件跟软件版本
     */
    fun readOtaVersion(
        type: String,
        eigenValue: Int = 0,
        otaListener: ((String, String) -> Unit)? = null,
    ) {
        var number = ""
        var revision = ""
        getDeviceBean(type).run {
            client.read(
                address,
                string2UUID(DeviceConstants.D_EQUIPMENT_INFORMATION),
                string2UUID(DeviceConstants.D_CHARACTER_2A24)
            ) { _, data1 ->
                if (data1 == null) {
                    return@read
                }
                modelNumber = DeviceConvert.bytesToAsciiString(data1)
                number = modelNumber
                if (number.isNotEmpty() && revision.isNotEmpty()) {
                    deviceConnectMap[type] = this
                    otaListener?.invoke(number, revision)
                }
            }
            client.read(
                address,
                string2UUID(DeviceConstants.D_EQUIPMENT_INFORMATION),
                string2UUID(if (eigenValue == 1) DeviceConstants.D_CHARACTER_2A26 else DeviceConstants.D_CHARACTER_2A28)
            ) { _, data1 ->
                if (data1 == null) {
                    return@read
                }
                modelRevision = DeviceConvert.bytesToAsciiString(data1)
                revision = modelRevision
                if (number.isNotEmpty() && revision.isNotEmpty()) {
                    deviceConnectMap[type] = this
                    otaListener?.invoke(number, revision)
                }
            }
        }
    }

    /**
     * 根据蓝牙名匹配
     * deviceName 指定单个设备名称
     */
    fun startSearch(listener: (name: String, mac: String) -> Unit, deviceName: String = "") {
        var needSearch = true//根据名称搜索到了就立刻关闭
        sentryPushListener?.invoke(
            PushLogLevel.INFO,
            "ble_scan",
            mapOf("isOpenBle" to isBluetoothOpened(), "bleName" to deviceName).vbToJson()
        )
        client.search(bleSearchRequest, object : SearchResponse {
            override fun onSearchStarted() {}
            override fun onDeviceFounded(device: SearchResult) {
                device.run {
                    if (name.isNullOrEmpty() || name.equals("NULL")) {
                        return
                    }
                    if (deviceName.isEmpty()) {
                        listener.invoke(name, address)
                    } else if (name == deviceName && needSearch) {
                        needSearch = false
                        stopSearch()
                        listener.invoke(name, address)
                    }
                    Log.d(logTag, "onDeviceFounded: $rssi $name $deviceName")
                }
            }

            override fun onSearchStopped() {
                if (needSearch) listener.invoke("", "")
            }

            override fun onSearchCanceled() {
                if (needSearch) listener.invoke("", "")
            }


        })
    }

    /**
     * 停止搜索
     */
    fun stopSearch() {
        client.stopSearch()
    }

    /**
     *根据设备类型、mac 获取设备bean
     * @param  params 可以是设备大类  也可以mac地址
     */
    fun getDeviceBean(params: String): DevicePropertyBean {
        //直接通过传入的值来判断 中间有没有:号,就是设备大类
        if (!params.contains(":")) {
            return deviceConnectMap[params] ?: DevicePropertyBean()
        } else {
            deviceConnectMap.forEach {
                if (it.value.address == params) {
                    return it.value
                }
            }
        }
        return DevicePropertyBean()
    }

    /**
     *根据设备类型、mac 获取连接状态
     */
    fun isConnect(deviceParams: String): Boolean {
        return if (deviceParams.isEmpty()) false else getDeviceBean(deviceParams).isConnect
    }

    /**
     * 断连
     */
    fun disConnect(mac: String, disConnectCurrentListener: ((mac: String) -> Unit)? = null) {
        if (mac.isEmpty()) return

        disConnectCurrentMac = mac
        client.disconnect(mac)
        this.disConnectCurrentListener = disConnectCurrentListener
    }

    /**
     *获取华为特征值
     */
    private fun getHWNotify(type: String): UUID? {
        return when (type) {
            DeviceConstants.D_ROW -> {
                string2UUID(DeviceConstants.D_SERVICE1826_2AD1)
            }

            DeviceConstants.D_BICYCLE -> {
                string2UUID(DeviceConstants.D_SERVICE1826_2AD2)
            }

            DeviceConstants.D_TECHNOGYM -> {
                string2UUID(DeviceConstants.D_SERVICE1826_2ACE)
            }

            else -> null
        }
    }

    /**
     * 蓝牙开关转态
     */
    fun isBluetoothOpened(): Boolean {
        return client.isBluetoothOpened
    }

    /**
     * 配对状态
     * @return BluetoothDevice.BOND_NONE
     */
    fun getBondStatus(address: String): Int {
        return client.getBondState(address)
    }

    /**
     * 取消配对
     */
    fun removeBondStatus(mac: String) {
        try {
            BluetoothUtils.getRemoteDevice(mac)?.run {
                val m = javaClass.getMethod("removeBond", *arrayOfNulls<Class<Any>>(0))
                m.invoke(this, *arrayOfNulls<Any>(0))
            }
        } catch (e: Exception) {
            Log.d(logTag, "removeBondStatus:$e")
        }
    }

    /**
     *请求开启蓝牙
     */
    @SuppressLint("MissingPermission")
    fun openBluetooth(
        context: FragmentActivity,
        onBluetoothStatusListener: ((Boolean) -> Unit)? = null,
    ) {
        this.onBluetoothStatusListener = onBluetoothStatusListener
        registerBluetoothStateListener(null)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !BluetoothUtils.isBluetoothEnabled()) {
            val intent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            context.startActivity(intent)
        } else {
            client.openBluetooth()
        }

    }

    /**
     *清除所有连接缓存
     */
    fun disAllConnect() {
        deviceConnectMap.forEach { (_, value) ->
            if (value.isConnect) {
                disConnect(mac = value.address)
            }
        }
        deviceConnectMap.clear()
    }

    /**
     *存储bean
     */
    fun saveConnectMap(bean: DevicePropertyBean) {
        deviceConnectMap[bean.type] = bean
    }

    /**
     * 断开的设备解绑订阅，防止连接设备失败还会回调
     */
    class BleStatusListener : BleConnectStatusListener() {
        override fun onConnectStatusChanged(mac: String, status: Int) {
            if (status != Constants.STATUS_CONNECTED) {
                client.unregisterConnectStatusListener(mac, this)
                val bean = getDeviceBean(mac)
                bean.isConnect = false
                disConnectListener?.invoke(mac)

                if (disConnectCurrentMac == mac) {
                    disConnectCurrentListener?.invoke(mac)
                    disConnectCurrentMac = ""
                }
                val map = mapOf("deviceType" to bean.type, "bleName" to bean.name, "bleMac" to mac)
                sentryPushListener?.invoke(
                    PushLogLevel.INFO,
                    "ble_disConnect_callback",
                    map.vbToJson()
                )
                writeToFile(logTag, "BleStatusListener:${getDeviceBean(mac)} $status")
            }
        }
    }
}
