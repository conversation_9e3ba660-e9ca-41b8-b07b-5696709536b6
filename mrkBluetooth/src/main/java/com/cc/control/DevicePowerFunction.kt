package com.cc.control


import com.cc.control.protocol.DeviceConstants
import com.cc.control.protocol.fff0Protocol
import com.cc.control.protocol.readZJModelId
import com.cc.control.protocol.writeZJBicycleClear
import com.cc.control.protocol.writeZJBicycleControl
import com.cc.control.protocol.writeZJBicycleData
import com.cc.control.protocol.writeZJBicycleStatus
import com.cc.control.protocol.writeZJInfo
import com.inuker.bluetooth.library.beacon.BeaconParser
import java.util.UUID

/**
 *  : cc
 *  : on 2022-02-20 12:54.
 *  :力量站
 */
class DevicePowerFunction(val type: String) : BaseDeviceFunction(type) {

    override fun start() {
        super.start()
        if (dateArray.isEmpty()) {
            dateArray.add(writeZJBicycleData())
            dateArray.add(writeZJBicycleStatus())
        }
        write(readZJModelId(), onSuccess =  ::writeData)
    }

    /**
     * 目前智健跑步机速度跟坡度一条指令
     * speed 速度 resistance 阻力 slope 坡度
     */
    override fun setControl(
        speed: Int,
        resistance: Int,
        slope: Int,
        isDelayed: Boolean,
        isSlope: Boolean,
    ) {
        setControl(writeZJBicycleControl(resistance, slope), false)
        writeToFile(TAG, "onsetControl$speed $resistance $readyConnect")
    }

    override fun getUnitDistance() {
        write(writeZJInfo())
    }

    override fun onBluetoothNotify(
        service: UUID,
        character: UUID,
        parser: BeaconParser,
        dataSize: Int,
    ) {
        BluetoothManager.getDeviceBean(type).run {
            fff0Protocol(notifyBean, type, parser, dataSize) { start, array ->
                if (protocol == DeviceConstants.D_SERVICE_TYPE_ZJ) {
                    mDataListener?.invoke(notifyBean)
                } else if (start) {
                    if (readyConnect) {
                        mDataListener?.invoke(notifyBean)
                    } else if (dateArray.isEmpty()) {
                        array?.run { dateArray.add(this) }
                        writeData()
                        readyConnect = true
                    } else {
                        //柏群重连需要重新启动数据交互
                        writeData()
                        readyConnect = true
                    }
                } else {
                    write(array)
                }
            }

        }
    }


    override fun onDestroyWrite(): ByteArray {
        return writeZJBicycleClear()
    }
}