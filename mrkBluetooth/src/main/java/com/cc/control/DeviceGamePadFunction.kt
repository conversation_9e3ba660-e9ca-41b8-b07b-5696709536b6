package com.cc.control

import android.util.Log
import com.cc.control.protocol.DeviceConstants
import com.inuker.bluetooth.library.beacon.BeaconParser
import java.util.*

/**
 * Author      : cc
 * Date        : on 2022-02-18 16:53.
 * Description :mrk 游戏手柄
 */
class DeviceGamePadFunction(val type: String) : BaseDeviceFunction(type) {
    override fun onBluetoothNotify(
        service: UUID,
        character: UUID,
        parser: BeaconParser,
        dataSize: Int,
    ) {
        if (BluetoothManager.getDeviceBean(type).protocol == DeviceConstants.D_SERVICE_TYPE_KEYBOARD) {
            //         中       上      右      左      下
            //单击按键  0xA0    0xA1    0xA2   0xA3    0xA4
            //长按按键  0xB0    0xB1    0xB2   0xB3    0xB4
            //长按释放  0xC0    0xC1    0xC2   0xC4    0xC4
            var key = -1
            var clickTime = -1//0短按1长按
            when (parser.readByte()) {
                0xA0 -> {//中
                    key = 4
                    clickTime = 0
                }
                0xA1 -> {//上
                    key = 1
                    clickTime = 0
                }
                0xA2 -> {//右
                    key = 0
                    clickTime = 0
                }
                0xA3 -> {//左
                    key = 3
                    clickTime = 0
                }
                0xA4 -> {//下
                    key = 2
                    clickTime = 0
                }
                0xB0 -> {
                    key = 4
                    clickTime = 1
                }
                0xB1 -> {
                    key = 1
                    clickTime = 1
                }
                0xB2 -> {
                    key = 0
                    clickTime = 1
                }
                0xB3 -> {
                    key = 3
                    clickTime = 1
                }
                0xB4 -> {
                    key = 2
                    clickTime = 1
                }
                0xC0 -> {
                    key = 4
                    clickTime = 2
                }
                0xC1 -> {
                    key = 1
                    clickTime = 2
                }
                0xC2 -> {
                    key = 0
                    clickTime = 2
                }
                0xC3 -> {
                    key = 3
                    clickTime = 2
                }
                0xC4 -> {
                    key = 2
                    clickTime = 2
                }
            }
            mGameDataListener?.invoke(key, clickTime)
            Log.d(TAG, "游戏手柄$key $clickTime")
        } else if (parser.readByte() == 0xAA && parser.readByte() == dataSize - 2 && parser.readByte() == 1) {
            //AA+包长+数据类型(00H：设备信息01H：运动数据 02H 控制命令 03H 功能测试)
            when (parser.readByte()) {
                0x00 -> {//电量
                    parser.readByte()
                }
                0x36 -> { //手柄按键 按键数据 0开始 时间 0短按 1长按
                    mGameDataListener?.invoke((parser.readByte()), parser.readByte())
                    Log.d(TAG, "游戏手柄")
                }
            }
        }
    }

    /**
     * 目前智健跑步机速度跟坡度一条指令
     * speed 速度 resistance 阻力 slope 坡度
     */
    override fun setControl(
        speed: Int,
        resistance: Int,
        slope: Int,
        isDelayed: Boolean,
        isSlope: Boolean,
    ) {
    }
    override fun onDestroyWrite(): ByteArray? {
        return null
    }
}