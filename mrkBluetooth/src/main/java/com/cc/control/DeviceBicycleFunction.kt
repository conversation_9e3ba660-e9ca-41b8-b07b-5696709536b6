package com.cc.control


import com.cc.control.BluetoothManager.sentryPushListener
import com.cc.control.enums.PushLogLevel
import com.cc.control.enums.TreadmillStatus
import com.cc.control.protocol.DeviceConstants
import com.cc.control.protocol.DeviceConstants.D_SERVICE1826
import com.cc.control.protocol.DeviceConstants.D_SERVICE_BQ
import com.cc.control.protocol.DeviceConstants.D_SERVICE_FFFO
import com.cc.control.protocol.DeviceConstants.D_SERVICE_MRK
import com.cc.control.protocol.fff0Protocol
import com.cc.control.protocol.ftmsProtocol
import com.cc.control.protocol.getDistanceUnit
import com.cc.control.protocol.mrkProtocol
import com.cc.control.protocol.readZJModelId
import com.cc.control.protocol.string2UUID
import com.cc.control.protocol.writeBQBicycle5Resistance
import com.cc.control.protocol.writeBQBicycle6Resistance
import com.cc.control.protocol.writeBQBicycleClear
import com.cc.control.protocol.writeBQBicycleConnect
import com.cc.control.protocol.writeFTMSControl
import com.cc.control.protocol.writeMrkModel
import com.cc.control.protocol.writeMrkResistance
import com.cc.control.protocol.writeMrkSlope
import com.cc.control.protocol.writeMrkStop
import com.cc.control.protocol.writeResistanceControl
import com.cc.control.protocol.writeSlopeControl
import com.cc.control.protocol.writeZJBicycleClear
import com.cc.control.protocol.writeZJBicycleControl
import com.cc.control.protocol.writeZJBicycleData
import com.cc.control.protocol.writeZJBicycleStatus
import com.cc.control.protocol.writeZJInfo
import com.inuker.bluetooth.library.beacon.BeaconParser
import com.inuker.bluetooth.library.utils.ByteUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID

/**
 *  : cc
 *  : on 2022-02-20 12:54.
 *  :单车协议
 */
class DeviceBicycleFunction(val type: String) : BaseDeviceFunction(type) {
    override fun start() {
        super.start()
        /**
         *2022-11立聪确认，兼容Merach-MR636D运行状态再发开始会倒着走
         */
        BluetoothManager.getDeviceBean(type).run {
            if (serviceUUID == null) return
            if (name.contains("Merach-MR636D")) {
                scope.launch {
                    //解决设备清零之后开启运动获取状态是运行中，导致无法开始
                    delay(1000)
                    read(serviceUUID!!, string2UUID(DeviceConstants.D_SERVICE1826_2AD3)) {
                        if (it.size >= 2 && it[1].toInt() != 0x0D) {
                            startWrite()
                        }
                    }
                }
            } else {
                startWrite()
            }
        }

    }

    private fun startWrite() {
        notifyBean.status = TreadmillStatus.DEVICE_TREADMILL_RUNNING.num
        when (BluetoothManager.getDeviceBean(type).protocol) {
            DeviceConstants.D_SERVICE_TYPE_ZJ -> {
                if (dateArray.isEmpty()) {
                    dateArray.add(writeZJBicycleData())
                    dateArray.add(writeZJBicycleStatus())
                }
                write(readZJModelId(), onSuccess = ::writeData)
            }

            DeviceConstants.D_SERVICE_TYPE_BQ -> {
                write(writeBQBicycleConnect())
            }

            DeviceConstants.D_SERVICE_TYPE_MRK -> {
                //   writeMrkStart()
            }

            else -> {  //开始指令华为部分设备用于结束训练之后恢复连接
                write(writeFTMSControl()) {
                    write(ByteUtils.stringToBytes("07"))
                }
            }
        }
    }

    override fun setControl(
        speed: Int,
        resistance: Int,
        slope: Int,
        isDelayed: Boolean,
        isSlope: Boolean,
    ) {
        BluetoothManager.getDeviceBean(type).run {
            setControl(
                when (protocol) {
                    DeviceConstants.D_SERVICE_TYPE_BQ -> {
                        if (type == DeviceConstants.D_ROW) {
                            writeBQBicycle5Resistance(resistance)
                        } else {
                            writeBQBicycle6Resistance(resistance)
                        }
                    }

                    DeviceConstants.D_SERVICE_TYPE_FTMS -> {
                        write(writeFTMSControl())
                        if (isSlope) {
                            writeSlopeControl(slope * 10)
                        } else {
                            writeResistanceControl(resistance)
                        }
                    }

                    DeviceConstants.D_SERVICE_TYPE_MRK -> {
                        if (isSlope) {
                            writeMrkSlope(slope)
                        } else {
                            writeMrkResistance(resistance)
                        }
                    }

                    else -> {
                        writeZJBicycleControl(resistance, slope)
                    }
                }, isDelayed
            )
            writeToFile(TAG, "setControl $name $speed $resistance $slope $readyConnect $isDelayed")
        }
    }

    //目前用于游戏单车
    override fun setMode(model: Int, targetNum: Int, onSuccess: (() -> Unit)) {
        write(writeMrkModel(model))
    }

    override fun getUnitDistance() {
        val bean = BluetoothManager.getDeviceBean(type)
        val map = mapOf("deviceType" to type, "bleCommunicationProtocol" to bean.protocol)
        sentryPushListener?.invoke(PushLogLevel.INFO, "ble_getUnit", map.vbToJson())
        when (bean.protocol) {
            DeviceConstants.D_SERVICE_TYPE_ZJ -> {
                write(writeZJInfo())
            }

            DeviceConstants.D_SERVICE_TYPE_MRK -> {
                write(getDistanceUnit())
            }
        }

    }

    override fun onBluetoothNotify(
        service: UUID,
        character: UUID,
        parser: BeaconParser,
        dataSize: Int,
    ) {
        BluetoothManager.getDeviceBean(type).run {
            when (service.toString()) {
                D_SERVICE_MRK -> {
                    mrkProtocol(notifyBean, parser, dataSize, mUnitDistanceListener)
                    mDataListener?.invoke(notifyBean)
                }

                D_SERVICE1826 -> {
                    ftmsProtocol(notifyBean, name, type, parser)
                    mDataListener?.invoke(notifyBean)
                }

                D_SERVICE_FFFO, D_SERVICE_BQ -> {
                    fff0Protocol(
                        notifyBean,
                        type,
                        parser,
                        dataSize,
                        mUnitDistanceListener
                    ) { start, array ->
                        if (protocol == DeviceConstants.D_SERVICE_TYPE_ZJ) {
                            mDataListener?.invoke(notifyBean)
                        } else if (start) {
                            if (readyConnect) {
                                mDataListener?.invoke(notifyBean)
                            } else if (dateArray.isEmpty()) {
                                array?.run { dateArray.add(this) }
                                writeData()
                                readyConnect = true
                            } else {
                                //柏群重连需要重新启动数据交互
                                writeData()
                                readyConnect = true
                            }
                        } else {
                            write(array)
                        }
                    }
                }

                else -> {}
            }
        }
    }

    override fun onDestroyWrite(): ByteArray {
        return when (BluetoothManager.getDeviceBean(type).protocol) {
            DeviceConstants.D_SERVICE_TYPE_BQ -> {
                writeBQBicycleClear()
            }

            DeviceConstants.D_SERVICE_TYPE_MRK -> {
                writeMrkStop()
            }

            else -> {
                writeZJBicycleClear()
            }
        }
    }
}