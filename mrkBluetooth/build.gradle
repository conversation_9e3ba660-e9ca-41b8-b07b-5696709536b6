plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'com.kezong.fat-aar'
}
apply from: "upload.gradle"

android {
    compileSdk AndroidVersions.compileSdkVersion

    defaultConfig {
        minSdk AndroidVersions.minSdkVersion
        targetSdk AndroidVersions.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11

    }
}
repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    compileOnly 'androidx.appcompat:appcompat:1.3.1'
    compileOnly 'androidx.core:core-ktx:1.3.1'
    compileOnly 'com.alibaba:fastjson:1.2.83'
    compileOnly 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    compileOnly 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1'
    compileOnly 'androidx.lifecycle:lifecycle-runtime-ktx:2.2.0'//lifecycleScope
    implementation 'no.nordicsemi.android:dfu:1.10.3'//DFU升级库两个都要
    implementation 'no.nordicsemi.android:ble-common:2.2.0'
    implementation 'com.inuker.bluetooth:library:1.4.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.2.0'
}