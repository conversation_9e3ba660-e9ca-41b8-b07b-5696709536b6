package com.mrk.demo

import android.app.Application
import com.mrk.device.MrkDeviceManger
import com.mrk.network.MrkNetworkConfig
import com.mrk.network.net.MrkNetOptions
import com.v.log.LogConfig
import com.v.log.VLog

/**
 * author  : ww
 * desc    :
 * time    : 2024/3/21 13:17
 */
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        MrkDeviceManger.init(this, true,true)
        //网络请求配置
//        val netOptions = MrkNetOptions.Builder()
//            .setBaseUrl("https://api-usa.merach.com/")
//            .setInterceptor(NetworkHeadInterceptor())
//            .build()
//        MrkNetworkConfig.init(netOptions)
    }
}