package com.mrk.demo

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.mrk.demo.databinding.ActivityDeviceItemBinding
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceSearchBean


/**
 * author  : ww
 * desc    :
 * time    : 2024/5/28 13:31:12
 */
class DeviceAdapter :
    RecyclerView.Adapter<DeviceAdapter.MyViewHolder>() {

    var data = ArrayList<DeviceSearchBean>()

    private var setConnectListener: ((DeviceSearchBean) -> Unit)? = null
    private var setItemListener: ((DeviceSearchBean) -> Unit)? = null
    fun setListener(connectListener: ((DeviceSearchBean) -> Unit), itemListener: ((DeviceSearchBean) -> Unit)) {
        this.setConnectListener = connectListener
        this.setItemListener = itemListener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ActivityDeviceItemBinding.inflate(inflater, parent, false)
        return MyViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        val item = data[position]
        holder.bind(item)
    }

    override fun getItemCount(): Int {
        return data.size
    }

    inner class MyViewHolder(private val binding: ActivityDeviceItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(item: DeviceSearchBean) {
            // 绑定数据到视图
            binding.run {
                tvName.text = item.modelName
                tvBluetoothName.text = item.bluetoothName
                tvMac.text = item.mac
                Glide.with(tvConnect.context).load(item.cover).into(ivCover)

                when (item.connectEnum) {
                    DeviceConnectEnum.ON -> {
                        tvConnect.text = "已连接"
                    }

                    DeviceConnectEnum.ING -> {
                        tvConnect.text = "连接中"
                    }

                    else -> {
                        tvConnect.text = "连接"
                    }
                }

                tvConnect.setOnClickListener { setConnectListener?.invoke(item) }
                ivCover.setOnClickListener { setItemListener?.invoke(item) }
            }


        }
    }

    fun addData(item: DeviceSearchBean) {
        data.add(item)
        notifyDataSetChanged()
    }

    fun refresh(bean: DeviceMangerBean) {
        for (i in data.indices) {
            if (data[i].mac == bean.connectBean.mac) {
                data[i].connectEnum = bean.connectEnum
                notifyDataSetChanged()
                break
            }
        }
    }
}