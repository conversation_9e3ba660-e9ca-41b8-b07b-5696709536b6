package com.mrk.demo.kotlin


import android.app.ProgressDialog
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.mrk.demo.DeviceAdapter
import com.mrk.demo.databinding.ActivityDeviceBinding
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.BluetoothEnum
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceSearchBean
import com.mrk.device.device.DeviceListener
import com.v.log.util.log


/**
 * author  :
 * desc    :
 * time    : 2024/5/28 13:28:12
 */
class KotlinDeviceSearchActivity : AppCompatActivity() {

    private lateinit var mDataBinding: ActivityDeviceBinding

    private val mAdapter = DeviceAdapter()

    private val loading by lazy {
        ProgressDialog(this).apply {
            setTitle("Loading")
            setMessage("Please wait...")
        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mDataBinding = ActivityDeviceBinding.inflate(layoutInflater)
        setContentView(mDataBinding.root)

        initView()
        registerDeviceListener()
    }

    private fun initView() {

        val recyclerView = mDataBinding.recyclerView
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = mAdapter

        mDataBinding.swipeRefreshLayout.setOnRefreshListener { startSearch() }
        mDataBinding.btSearch.setOnClickListener {
            mDataBinding.swipeRefreshLayout.isRefreshing = true
            startSearch()
        }
        mDataBinding.btSearchStop.setOnClickListener { MrkDeviceManger.stopSearch() }


        mAdapter.setListener({ item ->
            deviceConnect(item)
        }, { item ->
            if (MrkDeviceManger.isConnect(item.mac)) {
                val intent = Intent(
                    this@KotlinDeviceSearchActivity,
                    KotlinDeviceDetailsActivity::class.java
                )
                intent.putExtras(
                    bundleOf(
                        KotlinDeviceDetailsActivity.PRODUCT_ID to item.productId
                    )
                )
                <EMAIL>(intent)
            }

        })


    }

    /**
     * 监听所有设备连接的对象
     */
    private fun registerDeviceListener() {
        MrkDeviceManger
            .registerBluetoothStateListener {
                mDataBinding.tvBluetoothStatus.text = "蓝牙开关状态:${it}"
            }.registerDeviceListener(this, object : DeviceListener() {

                override fun onConnectStatus(isAutoReconnect: Boolean, bean: DeviceMangerBean) {
                    bean.connectEnum.name.log("全局的消息")
                    when (bean.connectEnum) {
                        DeviceConnectEnum.ON -> {
                            toast("连接成功")
                            loading.dismiss()
                        }

                        DeviceConnectEnum.OFF -> {
                            toast("断开连接")
                            loading.dismiss()
                        }

                        DeviceConnectEnum.ING -> {
                            loading.show()
                        }

                        DeviceConnectEnum.ERROR -> {
                            toast("连接失败")
                            loading.dismiss()
                        }
                    }
                    mAdapter.refresh(bean)
                }


            })
    }

    /**
     * 开始搜索
     */
    private fun startSearch() {
        mAdapter.data.clear()
        MrkDeviceManger.startSearch(this, showBindDevice = 1, onSearchStatus = {
            mDataBinding.tvSearchStatus.text = "设备搜索状态:${it.name}"
            if (it == BluetoothEnum.STOP) {
                mDataBinding.swipeRefreshLayout.isRefreshing = false
            }
        }, onSearchDevice = {
            it.connectEnum = MrkDeviceManger.getDeviceStatus(it.mac)
            mAdapter.addData(it)
        })
    }


    /**
     * 设备连接
     */
    private fun deviceConnect(bean: DeviceSearchBean) {
        MrkDeviceManger
            .create(
                this,
                bean
            ).connect(onAutoConnect = {
                MrkDeviceManger.disConnect(it.address) {
                    mDataBinding.tvSearchStatus.postDelayed({
                        deviceConnect(bean)
                    }, 300)
                }
            })
    }

    private fun toast(msg: String) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
    }
}