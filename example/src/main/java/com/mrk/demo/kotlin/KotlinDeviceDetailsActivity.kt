package com.mrk.demo.kotlin


import android.app.ProgressDialog
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.cc.control.bean.DeviceTrainBO
import com.cc.control.protocol.DeviceConstants
import com.mrk.demo.databinding.ActivityDeviceDetailsBinding
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceTreadmillEnum
import com.mrk.device.device.DeviceControl
import com.mrk.device.device.DeviceListener


/**
 * author  :
 * desc    :
 * time    : 2024-05-28 13:27:57
 */
class KotlinDeviceDetailsActivity : AppCompatActivity(), View.OnClickListener {

    private lateinit var mDataBinding: ActivityDeviceDetailsBinding

    companion object {
        //设备大类
        const val PRODUCT_ID = "productId"
    }

    private val loading by lazy {
        ProgressDialog(this).apply {
            setTitle("Loading");
            setMessage("Please wait...");
        }
    }


    private val productId by lazy {
        intent.extras!!.getString(PRODUCT_ID)!!
    }

    private var deviceControl: DeviceControl? = null

    private val deviceListener = object : DeviceListener() {

        override fun onConnectStatus(isAutoReconnect: Boolean, bean: DeviceMangerBean) {
            mDataBinding.tvConnectStatus.text =
                "${MrkDeviceManger.getTypeName(productId)}  连接状态:${bean.connectEnum}"

            when (bean.connectEnum) {
                DeviceConnectEnum.ON -> {
                    loading.dismiss()
                }

                DeviceConnectEnum.OFF -> {
                    loading.dismiss()
                }

                DeviceConnectEnum.ING -> {
                    loading.setTitle("连接中...")
                    loading.show()
                }

                DeviceConnectEnum.ERROR -> {
                    loading.dismiss()
                }
            }
        }

        override fun onDeviceTreadmillStatus(status: DeviceTreadmillEnum) {
            mDataBinding.tvDeviceStatus.text = "跑步机状态:${status}"

            when (status) {
                DeviceTreadmillEnum.COUNT_TIME -> {
                    loading.setTitle("跑步机启动中")
                    loading.show()
                }

                DeviceTreadmillEnum.SLOW_DOWN -> {
                    loading.setTitle("跑步机减速中")
                    loading.show()
                }

                DeviceTreadmillEnum.START -> {
                    loading.dismiss()
                    deviceControl?.deviceStart()
                }

                else -> {
                    loading.dismiss()
                }
            }
        }

        override fun onNotifyData(bean: DeviceTrainBO) {
            mDataBinding.tvContent.text = bean.toString()
            mDataBinding.tvResistance.text = bean.drag.toString()
            mDataBinding.tvSpeed.text = bean.speed.toString()
            mDataBinding.tvSlope.text = bean.gradient.toString()
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mDataBinding = ActivityDeviceDetailsBinding.inflate(layoutInflater)
        setContentView(mDataBinding.root)


        mDataBinding.btConnect.setOnClickListener(this)
        mDataBinding.btDisConnect.setOnClickListener(this)
        mDataBinding.btStart.setOnClickListener(this)
        mDataBinding.btPause.setOnClickListener(this)
        mDataBinding.btDataClear.setOnClickListener(this)
        mDataBinding.btRegisterNotify.setOnClickListener(this)
        mDataBinding.btUnRegisterNotify.setOnClickListener(this)
        mDataBinding.btResistance.setOnClickListener(this)
        mDataBinding.btSpeed.setOnClickListener(this)
        mDataBinding.btSlope.setOnClickListener(this)
    }

    private fun initDevice() {
        if (deviceControl == null) {
            mDataBinding.tvConnectStatus.text =
                "${MrkDeviceManger.getTypeName(productId)}  连接状态:${
                    MrkDeviceManger.getDeviceStatus(
                        productId
                    )
                }"

            MrkDeviceManger.getDeviceMangerBean(productId)?.run {
                mDataBinding.tvConnectInfo.text = this.toString()
                this.connectBean.run {
                    //连接设备是否为跑步机
                    if (this.productId == DeviceConstants.D_TREADMILL) {
                        mDataBinding.llTreadmill.visibility = View.VISIBLE
                    }
                }

                //是否支持设置坡度
                this.deviceDetails?.run {
                    if (this.productModelTsl.controlSlope == 1) {
                        mDataBinding.llSlope.visibility = View.VISIBLE
                    } else {
                        mDataBinding.llSlope.visibility = View.GONE
                    }
                }
            }
            deviceControl =
                MrkDeviceManger.create(this, productId)
                    .setOnDeviceListener(deviceListener)
                    .registerDevice()
        }
        deviceControl?.autoConnect()

    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.btConnect.id -> {
                connect()
            }

            mDataBinding.btDisConnect.id -> {
                deviceControl?.disConnect()
            }

            mDataBinding.btDataClear.id -> {
                deviceControl?.clearData()
            }

            mDataBinding.btRegisterNotify.id -> {
                deviceControl?.setNotifyData(true)
            }

            mDataBinding.btUnRegisterNotify.id -> {
                deviceControl?.setNotifyData(false)
            }

            mDataBinding.btResistance.id -> {
                deviceControl?.sendCommand(
                    resistance = mDataBinding.etResistance.text.toString().toInt()
                )
            }


            mDataBinding.btStart.id -> {
                deviceControl?.deviceStart()
            }

            mDataBinding.btPause.id -> {
                deviceControl?.devicePause()
            }


            mDataBinding.btSpeed.id -> {
                deviceControl?.sendCommand(
                    speed = mDataBinding.etSpeed.text.toString().toInt(),
                    slope = 0
                )
            }

            mDataBinding.btSlope.id -> {
                if (deviceControl?.getDeviceMangerBean()?.connectBean?.productId == DeviceConstants.D_TREADMILL) {
                    deviceControl?.sendCommand(
                        speed = mDataBinding.etSpeed.text.toString().toInt(),
                        slope = mDataBinding.etSlope.text.toString().toInt()
                    )
                } else {
                    deviceControl?.sendCommand(
                        resistance = mDataBinding.etResistance.text.toString().toInt(),
                        slope = mDataBinding.etSlope.text.toString().toInt()
                    )
                }

            }
        }
    }

    private fun connect() {
        deviceControl?.connect()
        deviceControl?.autoConnect()
    }


    override fun onResume() {
        super.onResume()
        initDevice()
    }
}