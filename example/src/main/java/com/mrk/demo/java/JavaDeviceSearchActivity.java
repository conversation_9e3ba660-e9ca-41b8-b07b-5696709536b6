package com.mrk.demo.java;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.mrk.demo.DeviceAdapter;
import com.mrk.demo.databinding.ActivityDeviceBinding;
import com.mrk.device.MrkDeviceManger;

import com.mrk.device.bean.BluetoothEnum;
import com.mrk.device.bean.DeviceMangerBean;
import com.mrk.device.bean.DeviceSearchBean;
import com.mrk.device.device.DeviceListener;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * author  : ww
 * desc    :
 * time    : 2024/5/28 13:54
 */
public class JavaDeviceSearchActivity extends AppCompatActivity {
    private ActivityDeviceBinding mDataBinding;
    private DeviceAdapter mAdapter = new DeviceAdapter();

    private ProgressDialog loading;

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mDataBinding = ActivityDeviceBinding.inflate(getLayoutInflater());
        setContentView(mDataBinding.getRoot());

        initView();
        registerDeviceListener();
    }

    private void initView() {
        loading = new ProgressDialog(this);
        loading.setTitle("Loading");
        loading.setMessage("Please wait...");

        RecyclerView recyclerView = mDataBinding.recyclerView;
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(mAdapter);

        mDataBinding.swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                startSearch();
            }
        });
        mDataBinding.btSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mDataBinding.swipeRefreshLayout.setRefreshing(true);
                startSearch();
            }
        });

        mDataBinding.btSearchStop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                MrkDeviceManger.INSTANCE.stopSearch();
            }
        });

        mAdapter.setListener(new Function1<DeviceSearchBean, Unit>() {
            @Override
            public Unit invoke(DeviceSearchBean deviceSearchBean) {
                deviceConnect(deviceSearchBean);
                return null;
            }
        }, new Function1<DeviceSearchBean, Unit>() {
            @Override
            public Unit invoke(DeviceSearchBean deviceSearchBean) {
                if (MrkDeviceManger.INSTANCE.isConnect(deviceSearchBean.getMac())) {
                    Intent intent = new Intent(
                            JavaDeviceSearchActivity.this,
                            JavaDeviceDetailsActivity.class
                    );
                    Bundle bundle = new Bundle();
                    bundle.putString(JavaDeviceDetailsActivity.PRODUCT_ID, deviceSearchBean.getProductId());
                    intent.putExtras(bundle);
                    startActivity(intent);
                }
                return null;
            }
        });

    }

    /**
     * 监听所有设备连接的对象
     */
    private void registerDeviceListener() {
        MrkDeviceManger.INSTANCE.registerBluetoothStateListener(new Function1<BluetoothEnum, Unit>() {
            @Override
            public Unit invoke(BluetoothEnum bluetoothEnum) {
                mDataBinding.tvBluetoothStatus.setText("蓝牙开关状态:" + bluetoothEnum.name());
                return null;
            }
        }).registerDeviceListener(this, new DeviceListener() {

            @Override
            public void onConnectStatus(boolean isAutoReconnect, @NonNull DeviceMangerBean bean) {
                switch (bean.getConnectEnum()) {
                    case ON:
                        toast("连接成功");
                        loading.dismiss();
                        break;
                    case OFF:
                        toast("断开连接");
                        loading.dismiss();
                        break;
                    case ING:
                        loading.show();
                        break;
                    case ERROR:
                        toast("连接失败");
                        loading.dismiss();
                        break;
                    default:
                        break;
                }
                mAdapter.refresh(bean);
            }
        });
    }

    /**
     * 开始搜索
     */
    private void startSearch() {
        mAdapter.getData().clear();
        MrkDeviceManger.INSTANCE.startSearch(this, new Function1<BluetoothEnum, Unit>() {
            @Override
            public Unit invoke(BluetoothEnum bluetoothEnum) {
                if (bluetoothEnum == BluetoothEnum.STOP) {
                    mDataBinding.swipeRefreshLayout.setRefreshing(false);
                }
                return null;
            }
        }, new Function1<DeviceSearchBean, Unit>() {
            @Override
            public Unit invoke(DeviceSearchBean deviceSearchBean) {
                deviceSearchBean.setConnectEnum(MrkDeviceManger.INSTANCE.getDeviceStatus(deviceSearchBean.getMac()));
                mAdapter.addData(deviceSearchBean);
                return null;
            }
        });

    }


    /**
     * 设备连接
     */
    private void deviceConnect(DeviceSearchBean bean) {
        //一个设备类型只能连接一台设备,连接前请先判断有设备是否连接
        if (MrkDeviceManger.INSTANCE.isConnect(bean.getProductId())) {
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("提示");
            builder.setMessage("已有相同类型设备连接,是否断开已连接设备再连接?");
            builder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                    MrkDeviceManger.INSTANCE.disConnect(bean.getProductId(), new Function1<String, Unit>() {
                        @Override
                        public Unit invoke(String s) {
                            deviceConnect(bean);
                            return null;
                        }
                    });
                }
            });

            builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });

            // 创建并显示弹窗
            builder.create().show();
        } else {
            MrkDeviceManger.INSTANCE.create(this, bean).connect();
        }
    }

    private void toast(String msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }
}
