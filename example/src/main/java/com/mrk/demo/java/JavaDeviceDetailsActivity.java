package com.mrk.demo.java;

import android.app.ProgressDialog;
import android.os.Bundle;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;

import com.cc.control.bean.DeviceTrainBO;
import com.cc.control.protocol.DeviceConstants;
import com.mrk.demo.R;
import com.mrk.demo.databinding.ActivityDeviceDetailsBinding;
import com.mrk.device.MrkDeviceManger;
import com.mrk.device.bean.DeviceGoConnectBean;
import com.mrk.device.bean.DeviceMangerBean;
import com.mrk.device.bean.DeviceTreadmillEnum;
import com.mrk.device.device.DeviceControl;
import com.mrk.device.device.DeviceListener;

/**
 * author  : ww
 * desc    :
 * time    : 2024/5/28 13:41
 */
public class JavaDeviceDetailsActivity extends AppCompatActivity implements View.OnClickListener {
    private ActivityDeviceDetailsBinding mDataBinding;

    public static final String PRODUCT_ID = "productId";

    private ProgressDialog loading;

    private String productId;

    private DeviceControl deviceControl;

    private final DeviceListener deviceListener = new DeviceListener() {
        @Override
        public void onConnectStatus(boolean isAutoReconnect, DeviceMangerBean bean) {
            mDataBinding.tvConnectStatus.setText("连接状态\n" + MrkDeviceManger.INSTANCE.getTypeName(productId) + " 连接状态:" + bean.getConnectEnum());


            switch (bean.getConnectEnum()) {
                case ON:
                    loading.dismiss();
                    break;
                case OFF:
                    loading.dismiss();
                    break;
                case ING:
                    loading.setTitle("连接中...");
                    loading.show();
                    break;
                case ERROR:
                    loading.dismiss();
                    break;
            }
        }

        @Override
        public void onDeviceTreadmillStatus(DeviceTreadmillEnum status) {
            mDataBinding.tvDeviceStatus.setText("跑步机状态:" + status);

            switch (status) {
                case COUNT_TIME:
                    loading.setTitle("跑步机启动中");
                    loading.show();
                    break;
                case SLOW_DOWN:
                    loading.setTitle("跑步机减速中");
                    loading.show();
                    break;
                case START:
                    loading.dismiss();
                    if (deviceControl != null) {
                        deviceControl.deviceStart();
                    }
                    break;
                default:
                    loading.dismiss();
                    break;
            }
        }

        @Override
        public void onNotifyData(DeviceTrainBO bean) {
            mDataBinding.tvContent.setText("数据回调\n" + bean.toString());
            mDataBinding.tvResistance.setText(Integer.toString(bean.getDrag()));
            mDataBinding.tvSpeed.setText(Integer.toString((int) bean.getSpeed()));
            mDataBinding.tvSlope.setText(Integer.toString(bean.getGradient()));
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mDataBinding = ActivityDeviceDetailsBinding.inflate(getLayoutInflater());
        setContentView(mDataBinding.getRoot());

        productId = getIntent().getStringExtra(PRODUCT_ID);

        loading = new ProgressDialog(this);
        loading.setTitle("Loading");
        loading.setMessage("Please wait...");

        mDataBinding.btConnect.setOnClickListener(this);
        mDataBinding.btDisConnect.setOnClickListener(this);
        mDataBinding.btStart.setOnClickListener(this);
        mDataBinding.btPause.setOnClickListener(this);
        mDataBinding.btDataClear.setOnClickListener(this);
        mDataBinding.btRegisterNotify.setOnClickListener(this);
        mDataBinding.btUnRegisterNotify.setOnClickListener(this);
        mDataBinding.btResistance.setOnClickListener(this);
        mDataBinding.btSpeed.setOnClickListener(this);
        mDataBinding.btSlope.setOnClickListener(this);

        initDevice();
    }

    private void initDevice() {
        if (deviceControl == null) {
            mDataBinding.tvConnectStatus.setText("连接状态\n" + MrkDeviceManger.INSTANCE.getTypeName(productId) + " 连接状态:" + MrkDeviceManger.INSTANCE.getDeviceStatus(productId));
            DeviceMangerBean bean = MrkDeviceManger.INSTANCE.getDeviceMangerBean(productId);
            if (bean.getConnectBean() != null) {
                mDataBinding.tvConnectInfo.setText("设备信息\n" + bean.toString());
                //连接设备是否为跑步机
                if (bean.getConnectBean().getProductId().equals(DeviceConstants.D_TREADMILL)) {
                    mDataBinding.llTreadmill.setVisibility(View.VISIBLE);
                }
            }
            //是否支持设置坡度
            if (bean.getDeviceDetails() != null) {
                if (bean.getDeviceDetails().getProductModelTsl().getControlSlope() == 1) {
                    mDataBinding.llSlope.setVisibility(View.VISIBLE);
                } else {
                    mDataBinding.llSlope.setVisibility(View.GONE);
                }
            }
            deviceControl = MrkDeviceManger.INSTANCE.create(this, productId)
                    .setOnDeviceListener(deviceListener)
                    .registerDevice();
        }
        deviceControl.autoConnect();

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btConnect:
                connect();
                break;
            case R.id.btDisConnect:
                deviceControl.disConnect();
                break;
            case R.id.btDataClear:
                deviceControl.clearData();
                break;
            case R.id.btRegisterNotify:
                deviceControl.setNotifyData(true);
                break;
            case R.id.btUnRegisterNotify:
                deviceControl.setNotifyData(false);
                break;
            case R.id.btResistance:
                deviceControl.sendCommandResistance(Integer.parseInt(mDataBinding.etResistance.getText().toString()));
                break;
            case R.id.btStart:
                deviceControl.deviceStart();
                break;
            case R.id.btPause:
                deviceControl.devicePause();
                break;
            case R.id.btSpeed:
                //跑步机只能发送组合指令,即不论发送坡度还是速度,都需要一起
                deviceControl.sendCommandTreadmill(Integer.parseInt(mDataBinding.etSpeed.getText().toString()), Integer.parseInt(mDataBinding.etSlope.getText().toString()));
            case R.id.btSlope:
                if (deviceControl.getDeviceMangerBean().getConnectBean().getProductId().equals(DeviceConstants.D_TREADMILL)) {
                    //跑步机只能发送组合指令,即不论发送坡度还是速度,都需要一起
                    deviceControl.sendCommandTreadmill(Integer.parseInt(mDataBinding.etSpeed.getText().toString()), Integer.parseInt(mDataBinding.etSlope.getText().toString()));
                } else {
                    deviceControl.sendCommandSlope(Integer.parseInt(mDataBinding.etSlope.getText().toString()));
                }
                break;
        }
    }

    private void connect() {
        deviceControl.connect();
        deviceControl.autoConnect();
    }

    @Override
    protected void onResume() {
        super.onResume();
        initDevice();
    }
}
