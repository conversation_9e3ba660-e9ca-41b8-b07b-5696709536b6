package com.mrk.demo


import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.mrk.demo.databinding.ActivityMainBinding
import com.mrk.demo.java.JavaDeviceSearchActivity
import com.mrk.demo.kotlin.KotlinDeviceSearchActivity
import com.mrk.device.MrkDeviceManger


/**
 * author  :
 * desc    :
 * time    : 2024-05-28 11:25:37
 */
class MainActivity : AppCompatActivity() {

    private lateinit var mDataBinding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        mDataBinding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(mDataBinding.root)

        mDataBinding.btKotlinDemo.setOnClickListener {
            startActivity(Intent(this, KotlinDeviceSearchActivity::class.java))
        }

        mDataBinding.btJavaDemo.setOnClickListener {
            startActivity(Intent(this, JavaDeviceSearchActivity::class.java))
        }
    }

    override fun onResume() {
        super.onResume()
        MrkDeviceManger.clear(this)
    }


}