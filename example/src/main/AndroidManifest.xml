<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <!--    蓝牙所需权限-->
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"
        tools:node="replace" />
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        tools:node="replace" />

    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <!-- Android 12，
   申请权限要么加上 Manifest.permission.ACCESS_FINE_LOCATION // 添加这行确保所有情况下蓝牙扫描都能正常工作
   要么在不申请定位权限时,必须加上android:usesPermissionFlags="neverForLocation"，否则搜不到设备 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <!--    蓝牙所需权限-->


    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MrkDeviceDemo">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.MrkDeviceDemo">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".java.JavaDeviceDetailsActivity" />
        <activity android:name=".java.JavaDeviceSearchActivity" />
        <activity android:name=".kotlin.KotlinDeviceSearchActivity" />
        <activity android:name=".kotlin.KotlinDeviceDetailsActivity" />
    </application>

</manifest>