<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvBluetoothStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:padding="10dp"
        android:text="蓝牙开关状态:" />

    <TextView
        android:id="@+id/tvSearchStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:paddingHorizontal="10dp"
        android:text="设备搜索状态:" />


    <TextView
        android:id="@+id/btSearch"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/purple_500"
        android:gravity="center"
        android:text="开始搜索"
        android:textColor="@color/white" />

    <TextView
        android:id="@+id/btSearchStop"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:background="@color/purple_500"
        android:gravity="center"
        android:text="停止搜索"
        android:textColor="@color/white" />


    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:listitem="@layout/activity_device_item" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>