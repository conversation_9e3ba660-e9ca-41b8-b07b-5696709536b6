package com.mrk.scale

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner


/**
 * author  : ww
 * desc    :
 * time    : 2024/4/17 17:30
 */
class DeviceScaleListenerManager {

    private val listenerList = mutableListOf<DeviceScaleListener>()

    fun addListener(owner: Lifecycle?, listener: DeviceScaleListener) {
        addListener(listener)

        owner?.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                if (event == Lifecycle.Event.ON_START) {
//                    addListener(listener)
                } else if (event == Lifecycle.Event.ON_STOP) {
//                    removeListener(listener)
                } else if (event == Lifecycle.Event.ON_DESTROY) {
                    // 如果LifecycleOwner被销毁，确保移除这个observer，避免内存泄漏
                    MrkScaleManager.clear(false)
                    removeListener(listener)
                    owner.removeObserver(this)
                }
            }
        })
    }


    fun forEachListener(action: (DeviceScaleListener) -> Unit) {
        listenerList.forEach {
            action(it)
        }
    }


    private fun addListener(listener: DeviceScaleListener) {
        // 添加监听器逻辑
        listenerList.add(listener)
    }

     fun removeListener(listener: DeviceScaleListener) {
        // 移除监听器逻辑
        listenerList.remove(listener)
    }

    fun clear() {
        listenerList.clear()
    }
}