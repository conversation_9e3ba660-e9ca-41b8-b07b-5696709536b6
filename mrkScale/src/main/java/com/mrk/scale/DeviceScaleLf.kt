package com.mrk.scale

import android.content.Context
import com.mrk.scale.bean.DeviceScaleBrandEnum
import com.mrk.scale.bean.DeviceScaleConnectEnum
import com.mrk.scale.bean.DeviceScaleDataBean
import com.mrk.scale.bean.DeviceScaleMeasureEnum
import com.mrk.scale.bean.DeviceScaleSearchBean
import com.mrk.scale.bean.DeviceScaleSearchEnum
import com.mrk.scale.bean.DeviceScaleTypeEnum
import com.peng.ppscale.PPBlutoothKit
import com.peng.ppscale.business.ble.listener.PPBleStateInterface
import com.peng.ppscale.business.ble.listener.PPDataChangeListener
import com.peng.ppscale.business.ble.listener.PPSearchDeviceInfoInterface
import com.peng.ppscale.business.device.PPUnitType
import com.peng.ppscale.business.state.PPBleSwitchState
import com.peng.ppscale.business.state.PPBleWorkState
import com.peng.ppscale.device.PPBlutoothPeripheralBaseController
import com.peng.ppscale.device.PeripheralApple.PPBlutoothPeripheralAppleController
import com.peng.ppscale.device.PeripheralIce.PPBlutoothPeripheralIceController
import com.peng.ppscale.search.PPSearchManager
import com.peng.ppscale.util.DeviceUtil
import com.peng.ppscale.util.PPUtil
import com.peng.ppscale.vo.PPBodyBaseModel
import com.peng.ppscale.vo.PPBodyFatModel
import com.peng.ppscale.vo.PPDeviceModel
import com.peng.ppscale.vo.PPScaleDefine
import com.peng.ppscale.vo.PPScaleDefine.PPDeviceAccuracyType
import com.peng.ppscale.vo.PPUserGender
import com.peng.ppscale.vo.PPUserModel

/**
 * author  : ww
 * desc    : 乐福体脂称
 * time    : 2024/04/07 14:56
 */
class DeviceScaleLf : DeviceScaleBaseFunction() {

    private var bodyModel: PPBodyBaseModel? = null


    //乐福体脂秤
    private val ppScale by lazy {
        PPSearchManager()
    }

    //乐福体脂秤控制类
    private var ppScaleController: PPBlutoothPeripheralBaseController? = null


    private fun getUserModel(): PPUserModel {
        return PPUserModel.Builder()
            .setSex(if (MrkScaleManager.getScaleUserBean().sex == 1) PPUserGender.PPUserGenderMale else PPUserGender.PPUserGenderFemale) //gender
            .setHeight(MrkScaleManager.getScaleUserBean().height.toInt())//height 100-220
            .setAge(calculateAge())//age 10-99
            .build()
    }


    private fun getDeviceType(type: PPScaleDefine.PPDeviceCalcuteType): DeviceScaleTypeEnum {
        return when (type) {
            PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate8,
            PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate8_0,
            PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate8_1,
            PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate8_2,
            PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate8_3,
            -> {
                //"8电极"
                return DeviceScaleTypeEnum.TYPE_8

            }

            PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate4_1,
            PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate4_0,
            PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate
            -> {
                //"4电极"
                return DeviceScaleTypeEnum.TYPE_4
            }

            else -> {
                //"未知"
                return DeviceScaleTypeEnum.TYPE_UNKNOWN
            }
        }
    }

    /**
     * 搜索设备回调
     */
    private var searchDeviceInfoInterface = PPSearchDeviceInfoInterface { model, data ->
        if (model != null) {
            if (!uniqueData.contains(model.deviceMac)) {
                uniqueData.add(model.deviceMac)
                log("SearchSuccessData:$data   ${model.toString()}")
                // 数据不存在，添加到集合中
                MrkScaleManager.getListenerManager()
                    .forEachListener {
                        it.onSearchData(
                            DeviceScaleSearchBean(
                                deviceName = model.deviceName,
                                deviceMac = model.deviceMac,
                                brandEnum = DeviceScaleBrandEnum.BRAND_LF,
                                typeEnum = getDeviceType(model.deviceCalcuteType),
                                connectEnum = DeviceScaleConnectEnum.OFF
                            )
                        )
                    }


            }
        }

    }

    /**
     * LF 状态回调接口
     */
    private var bleStateInterface: PPBleStateInterface = object : PPBleStateInterface() {
        override fun monitorBluetoothWorkState(
            state: PPBleWorkState,
            deviceModel: PPDeviceModel?,
        ) {
            when (state) {
                PPBleWorkState.PPBleWorkStateSearching -> {
                    MrkScaleManager.getListenerManager()
                        .forEachListener { it.onSearchStatus(DeviceScaleSearchEnum.ING) }
                }

                PPBleWorkState.PPBleWorkSearchTimeOut,//"扫描超时"
                PPBleWorkState.PPBleWorkSearchFail,//"扫描失败"
                PPBleWorkState.PPBleStateSearchCanceled,//"停止扫描"
                -> {
                    log("扫描超时")
                    MrkScaleManager.getListenerManager()
                        .forEachListener { it.onSearchStatus(DeviceScaleSearchEnum.STOP) }

                }

                PPBleWorkState.PPBleWorkStateConnecting -> {
                    ppScale.stopSearch()
                    setConnectStatus(DeviceScaleConnectEnum.ING)
                }

                PPBleWorkState.PPBleWorkStateConnected -> {
                    setConnectStatus(DeviceScaleConnectEnum.ON)
                }

                PPBleWorkState.PPBleWorkStateConnectFailed -> {
                    setConnectStatus(DeviceScaleConnectEnum.OFF)
                }

                PPBleWorkState.PPBleWorkStateDisconnected -> {
                    setConnectStatus(DeviceScaleConnectEnum.OFF)
                }

                PPBleWorkState.PPBleWorkStateWritable -> {
                }
                else ->
                {

                }
            }

        }

        override fun monitorBluetoothSwitchState(ppBleSwitchState: PPBleSwitchState) {
            when (ppBleSwitchState) {
                PPBleSwitchState.PPBleSwitchStateOff -> {
                    setConnectStatus(DeviceScaleConnectEnum.OFF)
                }

                PPBleSwitchState.PPBleSwitchStateOn -> {
                }

                else -> {

                }
            }
        }

    }

    /**
     * 称重数据回调
     */
    private val changeListener = object : PPDataChangeListener {


        //过程数据
        override fun monitorProcessData(
            bodyBaseModel: PPBodyBaseModel?,
            deviceModel: PPDeviceModel?
        ) {
            if (bodyBaseModel == null) {
                return
            }
            val weightStr = PPUtil.getWeightValueD(
                bodyBaseModel.unit,
                bodyBaseModel.getPpWeightKg().toDouble(),
                deviceModel!!.deviceAccuracyType.getType()
            )
            MrkScaleManager.getListenerManager().forEachListener {
                it.onDataProcess(
                    weightStr
                )
                it.onDeviceScaleMeasureStatus(DeviceScaleMeasureEnum.MEASURE_WEIGHT_ING)
            }

            log("过程数据:${weightStr}")
        }

        override fun onImpedanceFatting() {
            log("体脂测量中")
            MrkScaleManager.getListenerManager()
                .forEachListener { it.onDeviceScaleMeasureStatus(DeviceScaleMeasureEnum.MEASURE_BODY_FAT_ING) }
        }

        // 锁定数据
        override fun monitorLockData(
            bodyBaseModel: PPBodyBaseModel?,
            deviceModel: PPDeviceModel?
        ) {
            if (bodyBaseModel?.isHeartRating == true) {
                MrkScaleManager.getListenerManager()
                    .forEachListener { it.onDeviceScaleMeasureStatus(DeviceScaleMeasureEnum.MEASURE_HEART_RATE_ING) }
                //心率测量中
                log("心率测量中:${bodyBaseModel.toString()}")
            } else {
                <EMAIL> = bodyBaseModel
                resetScale()
            }
        }

        override fun monitorOverWeight() {
            //超重
            MrkScaleManager.getListenerManager()
                .forEachListener { it.onMonitorOverWeight() }
            log("onMonitorOverWeight")
        }


        override fun monitorDataFail(bodyBaseModel: PPBodyBaseModel?, deviceModel: PPDeviceModel?) {
        }


    }

    override fun init(context: Context) {
        brandEnum = DeviceScaleBrandEnum.BRAND_LF
        PPBlutoothKit.setDebug(true)
        PPBlutoothKit.initSdk(
            context,
            "lefu258235e2917ca1b8",
            "fTGXWk+Ayu1q27EhKos6G4C3hxjv6g0Qmnr/X1MpOKw=",
            "lefu.config"
        )
        super.init(context)
    }

    override fun startSearch() {
        super.startSearch()
        ppScale.startSearchDeviceList(
            15000,
            searchDeviceInfoInterface,
            bleStateInterface
        )
    }

    override fun stopSearch() {
        super.stopSearch()
        ppScale.stopSearch()
    }

    override fun connect(bean: DeviceScaleSearchBean) {
        super.connect(bean)
        //注册数据变化监听

        if (ppScaleController == null) {
            ppScaleController = if (bean.typeEnum == DeviceScaleTypeEnum.TYPE_4) {
                //四电级控制类
                PPBlutoothPeripheralAppleController().apply {
                    registDataChangeListener(changeListener)
                }
            } else {
                //八电级控制类
                PPBlutoothPeripheralIceController().apply {
                    registDataChangeListener(changeListener)
                }
            }
        }
        ppScaleController?.startConnect(
            PPDeviceModel(bean.deviceMac, bean.deviceName),
            bleStateInterface
        )
    }

    override fun disConnect() {
        super.disConnect()
        ppScaleController?.disConnect()
    }

    override fun clear() {
        disConnect()
        bodyModel = null

        super.clear()


    }

    private fun formatData(): PPBodyFatModel {

        val bodyBaseModel = PPBodyBaseModel()

        bodyModel?.run {
            val weight = ((this.getPpWeightKg().toDouble() + 0.005) * 100).toInt()
            val deviceModel = PPDeviceModel(
                currentScaleBean!!.deviceMac,
                currentScaleBean!!.deviceName
            )

            log("getUserModel():${getUserModel()}")
            bodyBaseModel.weight = weight
            bodyBaseModel.userModel = getUserModel()
            bodyBaseModel.unit = PPUnitType.Unit_KG

            if (currentScaleBean!!.typeEnum == DeviceScaleTypeEnum.TYPE_4) {
                deviceModel.deviceCalcuteType =
                    PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate
                bodyBaseModel.impedance =
                    if (this.impedance <= 0L) 4195332L else this.impedance
                deviceModel.deviceAccuracyType =
                    if (DeviceUtil.Point2_Scale_List.contains(deviceModel.deviceName)) {
                        PPDeviceAccuracyType.PPDeviceAccuracyTypePoint005
                    } else {
                        PPDeviceAccuracyType.PPDeviceAccuracyTypePoint01
                    }
                log("四电级计算")
            } else {
                bodyBaseModel.heartRate = this.heartRate
                deviceModel.deviceCalcuteType =
                    PPScaleDefine.PPDeviceCalcuteType.PPDeviceCalcuteTypeAlternate8
                bodyBaseModel.z100KhzLeftArmEnCode = this.z100KhzLeftArmEnCode
                bodyBaseModel.z100KhzLeftLegEnCode = this.z100KhzLeftLegEnCode
                bodyBaseModel.z100KhzRightArmEnCode = this.z100KhzRightArmEnCode
                bodyBaseModel.z100KhzRightLegEnCode = this.z100KhzRightLegEnCode
                bodyBaseModel.z100KhzTrunkEnCode = this.z100KhzTrunkEnCode
                bodyBaseModel.z20KhzLeftArmEnCode = this.z20KhzLeftArmEnCode
                bodyBaseModel.z20KhzLeftLegEnCode = this.z20KhzLeftLegEnCode
                bodyBaseModel.z20KhzRightArmEnCode = this.z20KhzRightArmEnCode
                bodyBaseModel.z20KhzRightLegEnCode = this.z20KhzRightLegEnCode
                bodyBaseModel.z20KhzTrunkEnCode = this.z20KhzTrunkEnCode
                log("八电级计算")
            }
            bodyBaseModel.deviceModel = deviceModel
        }


        log("bodyBaseModel:$bodyBaseModel")
        return PPBodyFatModel(bodyBaseModel)
    }

    override fun resetScale(resetScaleListener: ((DeviceScaleDataBean) -> Unit)?) {
        super.resetScale(resetScaleListener)
        if (bodyModel == null || currentScaleBean == null) {
            return
        }

        log("锁定数据:${bodyModel!!}")
        val formatModel = formatData()
        log("计算数据:${formatModel}")


        val weightStr = PPUtil.getWeightValueD(
            bodyModel!!.unit,
            formatModel.ppWeightKg.toDouble(),
            PPDeviceAccuracyType.PPDeviceAccuracyTypePoint005.getType()
        )
        val bean = formatScaleData(formatModel)
        log("上报数据:${bean}")

        if (resetScaleListener == null) {
            MrkScaleManager.getListenerManager().forEachListener {
                it.onDataSuccess(
                    weightStr,
                    formatScaleData(formatModel)
                )
                it.onDeviceScaleMeasureStatus(DeviceScaleMeasureEnum.MEASURE_END)
            }
        } else {
            resetScaleListener.invoke(bean)
        }
    }

}
