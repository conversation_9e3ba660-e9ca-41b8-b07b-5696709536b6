package com.mrk.scale

import android.content.Context
import cn.icomon.icdevicemanager.ICDeviceManager
import cn.icomon.icdevicemanager.ICDeviceManagerDelegate
import cn.icomon.icdevicemanager.model.data.ICCoordData
import cn.icomon.icdevicemanager.model.data.ICKitchenScaleData
import cn.icomon.icdevicemanager.model.data.ICRulerData
import cn.icomon.icdevicemanager.model.data.ICSkipData
import cn.icomon.icdevicemanager.model.data.ICWeightCenterData
import cn.icomon.icdevicemanager.model.data.ICWeightData
import cn.icomon.icdevicemanager.model.data.ICWeightHistoryData
import cn.icomon.icdevicemanager.model.device.ICDevice
import cn.icomon.icdevicemanager.model.device.ICDeviceInfo
import cn.icomon.icdevicemanager.model.device.ICUserInfo
import cn.icomon.icdevicemanager.model.other.ICConstant
import cn.icomon.icdevicemanager.model.other.ICConstant.ICAddDeviceCallBackCode
import cn.icomon.icdevicemanager.model.other.ICConstant.ICPeopleType
import cn.icomon.icdevicemanager.model.other.ICDeviceManagerConfig
import com.mrk.scale.bean.DeviceScaleBrandEnum
import com.mrk.scale.bean.DeviceScaleConnectEnum
import com.mrk.scale.bean.DeviceScaleDataBean
import com.mrk.scale.bean.DeviceScaleMeasureEnum
import com.mrk.scale.bean.DeviceScaleSearchBean
import com.mrk.scale.bean.DeviceScaleSearchEnum
import com.mrk.scale.bean.DeviceScaleTypeEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * author  : ww
 * desc    : 沃莱体脂称
 * time    : 2024/04/07 14:56
 */
class DeviceScaleWl :
    DeviceScaleBaseFunction() {

    private var countdownJob: Job? = null

    private var weightData: ICWeightData? = null


    private val deviceManagerListener = object : ICDeviceManagerDelegate {
        override fun onInitFinish(p0: Boolean) {
            //SDK初始化完成回调
            val userInfo = ICUserInfo()
            userInfo.age = calculateAge()
            userInfo.height = MrkScaleManager.getScaleUserBean().height.toInt()
            userInfo.sex =
                if (MrkScaleManager.getScaleUserBean().sex == 1) ICConstant.ICSexType.ICSexTypeMale else ICConstant.ICSexType.ICSexTypeFemal
            userInfo.peopleType = ICPeopleType.ICPeopleTypeNormal
            ICDeviceManager.shared().updateUserInfo(userInfo)

        }

        override fun onBleState(p0: ICConstant.ICBleState) {
            //蓝牙改变状态回调
            when (p0) {
                ICConstant.ICBleState.ICBleStatePoweredOn -> {
                    //蓝牙打开
                }

                ICConstant.ICBleState.ICBleStatePoweredOff -> {
                    //蓝牙关闭
                    setConnectStatus(DeviceScaleConnectEnum.OFF)
                }

                ICConstant.ICBleState.ICBleStateUnknown -> {
                    //未知状态
                }

                ICConstant.ICBleState.ICBleStateUnsupported -> {
                    //手机不支持BLE
                }

                ICConstant.ICBleState.ICBleStateUnauthorized -> {
                    //未获取到蓝牙授权
                }

                ICConstant.ICBleState.ICBleStateException -> {
                    //异常
                }

            }
        }

        override fun onDeviceConnectionChanged(
            p0: ICDevice?,
            p1: ICConstant.ICDeviceConnectState
        ) {
            //设备连接状态回调
            when (p1) {
                ICConstant.ICDeviceConnectState.ICDeviceConnectStateConnected -> {
                    //已连接
                    setConnectStatus(DeviceScaleConnectEnum.ON)
                }

                ICConstant.ICDeviceConnectState.ICDeviceConnectStateDisconnected -> {
                    //已断开
                    setConnectStatus(DeviceScaleConnectEnum.OFF)
                }
            }
        }

        override fun onReceiveBattery(device: ICDevice?, battery: Int, ext: Any?) {

        }


        override fun onReceiveWeightData(p0: ICDevice?, data: ICWeightData) {
            //体重秤数据回调
            if (data.isStabilized)//锁定数据
            {
                weightData = data
                resetScale()
            } else {
                val weigh = numberFormat(data.weight_kg)
                log("过程数据:$weigh")
                MrkScaleManager.getListenerManager()
                    .forEachListener {
                        it.onDataProcess(weigh)
                        it.onDeviceScaleMeasureStatus(DeviceScaleMeasureEnum.MEASURE_WEIGHT_ING)
                    }
            }
        }


        override fun onReceiveWeightCenterData(p0: ICDevice?, p1: ICWeightCenterData?) {
            //平衡数据回调，注:仅部分设备支持
        }

        override fun onReceiveWeightUnitChanged(p0: ICDevice?, p1: ICConstant.ICWeightUnit?) {
            //设备单位改变回调
        }


        override fun onReceiveMeasureStepData(
            device: ICDevice,
            step: ICConstant.ICMeasureStep,
            data: Any
        ) {
            //体重秤数据回调，注:部分设备不通过这个接口回调
            when (step) {
                ICConstant.ICMeasureStep.ICMeasureStepMeasureWeightData -> {
                    //测量体重 (ICWeightData)
                    val data = data as ICWeightData
                    onReceiveWeightData(device, data)
                }

                ICConstant.ICMeasureStep.ICMeasureStepMeasureCenterData -> {
                    //测量平衡 (ICWeightCenterData)
                    val data = data as ICWeightCenterData
                    onReceiveWeightCenterData(device, data)
                }

                ICConstant.ICMeasureStep.ICMeasureStepAdcStart -> {
                    //开始测量阻抗
                }

                ICConstant.ICMeasureStep.ICMeasureStepAdcResult -> {
                    //测量阻抗结束 (ICWeightData)
                }

                ICConstant.ICMeasureStep.ICMeasureStepHrStart -> {
                    //开始测量心率
                }

                ICConstant.ICMeasureStep.ICMeasureStepHrResult -> {
                    //测量心率结束 (ICWeightData)
                }

                ICConstant.ICMeasureStep.ICMeasureStepMeasureOver -> {
                    //测量结束
                    val data = data as ICWeightData
                    onReceiveWeightData(device, data)
                }

                else -> {

                }
            }
        }

        override fun onReceiveCoordData(p0: ICDevice?, p1: ICCoordData?) {
        }


        override fun onReceiveRulerUnitChanged(p0: ICDevice?, p1: ICConstant.ICRulerUnit?) {
        }

        override fun onReceiveKitchenScaleData(p0: ICDevice?, p1: ICKitchenScaleData?) {

        }

        override fun onNodeConnectionChanged(
            p0: ICDevice?,
            p1: Int,
            p2: ICConstant.ICDeviceConnectState?
        ) {
        }

        override fun onReceiveKitchenScaleUnitChanged(
            p0: ICDevice?,
            p1: ICConstant.ICKitchenScaleUnit?
        ) {

        }


        override fun onReceiveRulerData(p0: ICDevice?, p1: ICRulerData?) {

        }

        override fun onReceiveRulerHistoryData(p0: ICDevice?, p1: ICRulerData?) {
        }


        override fun onReceiveRulerMeasureModeChanged(
            p0: ICDevice?,
            p1: ICConstant.ICRulerMeasureMode?
        ) {

        }

        override fun onReceiveWeightHistoryData(p0: ICDevice?, p1: ICWeightHistoryData?) {
        }

        override fun onReceiveSkipData(p0: ICDevice?, p1: ICSkipData?) {
        }

        override fun onReceiveHistorySkipData(p0: ICDevice?, p1: ICSkipData?) {
        }


        override fun onReceiveUpgradePercent(
            p0: ICDevice?,
            p1: ICConstant.ICUpgradeStatus?,
            p2: Int
        ) {
        }

        override fun onReceiveDeviceInfo(p0: ICDevice?, p1: ICDeviceInfo?) {
        }

        override fun onReceiveDebugData(p0: ICDevice?, p1: Int, p2: Any?) {
        }

        override fun onReceiveConfigWifiResult(
            p0: ICDevice?,
            p1: ICConstant.ICConfigWifiState?
        ) {
        }

        override fun onReceiveHR(p0: ICDevice?, p1: Int) {
        }

        override fun onReceiveUserInfo(p0: ICDevice?, p1: ICUserInfo?) {
        }

        override fun onReceiveUserInfoList(p0: ICDevice?, p1: MutableList<ICUserInfo>?) {
        }

        override fun onReceiveRSSI(p0: ICDevice?, p1: Int) {
        }

    }

    override fun init(context: Context) {
        brandEnum = DeviceScaleBrandEnum.BRAND_WL
        val config = ICDeviceManagerConfig()
        config.context = context
        ICDeviceManager.shared().delegate = deviceManagerListener
        ICDeviceManager.shared().initMgrWithConfig(config)
        super.init(context)
    }


    override fun startSearch() {
        super.startSearch()
        countdownJob?.cancel() // 取消之前的倒计时任务
        countdownJob = CoroutineScope(Dispatchers.Main).launch {
            MrkScaleManager.getListenerManager()
                .forEachListener {
                    it.onSearchStatus(DeviceScaleSearchEnum.ING)
                }

            for (seconds in 15 downTo 0) {
                delay(1000)
            }
            stopSearch()
        }

        MrkScaleManager.getListenerManager()
            .forEachListener {
                it.onSearchStatus(DeviceScaleSearchEnum.START)
            }

        ICDeviceManager.shared().scanDevice { bean ->
            val type = bean.type
            //体重秤  脂肪秤  脂肪秤(带温度显示)
            if (!uniqueData.contains(bean.macAddr) &&
                (type == ICConstant.ICDeviceType.ICDeviceTypeWeightScale || type == ICConstant.ICDeviceType.ICDeviceTypeFatScale || type == ICConstant.ICDeviceType.ICDeviceTypeFatScaleWithTemperature)
            ) {
                log("SearchData:${bean}")
                // 数据不存在，添加到集合中
                uniqueData.add(bean.macAddr)
                MrkScaleManager.getListenerManager()
                    .forEachListener {
                        it.onSearchData(
                            DeviceScaleSearchBean(
                                deviceName = bean.name,
                                deviceMac = bean.macAddr,
                                brandEnum = DeviceScaleBrandEnum.BRAND_LF,
                                typeEnum = DeviceScaleTypeEnum.TYPE_4,
                                connectEnum = DeviceScaleConnectEnum.OFF
                            )
                        )
                    }

            }

        }

    }

    override fun stopSearch() {
        super.stopSearch()
        countdownJob?.cancel()
        ICDeviceManager.shared().stopScan()
        MrkScaleManager.getListenerManager()
            .forEachListener {
                it.onSearchStatus(DeviceScaleSearchEnum.STOP)
            }
    }

    override fun connect(bean: DeviceScaleSearchBean) {
        stopSearch()
        super.connect(bean)
        val device = ICDevice()
        device.macAddr = bean.deviceMac
        ICDeviceManager.shared().addDevice(device) { icDevice, status ->
            if (status == ICAddDeviceCallBackCode.ICAddDeviceCallBackCodeSuccess) {

            } else {
                val msg = when (status) {
                    ICAddDeviceCallBackCode.ICAddDeviceCallBackCodeSuccess ->
                        "添加成功,不代表连接成功"

                    ICAddDeviceCallBackCode.ICAddDeviceCallBackCodeFailedAndSDKNotInit ->
                        "添加失败,SDK未初始化"

                    ICAddDeviceCallBackCode.ICAddDeviceCallBackCodeFailedAndExist ->
                        "添加失败,设备已存在,无需重复添加"

                    ICAddDeviceCallBackCode.ICAddDeviceCallBackCodeFailedAndDeviceParamError ->
                        "添加失败,设备参数有错"
                }
                log("连接失败:${msg}")
                setConnectStatus(DeviceScaleConnectEnum.OFF)
            }
        }
    }

    override fun disConnect() {
        super.disConnect()
        weightData = null
        val device = ICDevice()
        device.macAddr = currentScaleBean?.deviceMac
        ICDeviceManager.shared().removeDevice(
            device
        ) { p0, p1 -> }
    }

    override fun clear() {
        disConnect()
        super.clear()
    }


    override fun resetScale(resetScaleListener: ((DeviceScaleDataBean) -> Unit)?) {
        super.resetScale(resetScaleListener)
        if (weightData == null || currentScaleBean == null) {
            return
        }

        val userInfo = ICUserInfo()
        userInfo.kitchenUnit = ICConstant.ICKitchenScaleUnit.ICKitchenScaleUnitG
        userInfo.rulerUnit = ICConstant.ICRulerUnit.ICRulerUnitInch
        userInfo.peopleType = ICPeopleType.ICPeopleTypeNormal//用户类型
        userInfo.age = calculateAge()
        userInfo.height = MrkScaleManager.getScaleUserBean().height.toDouble().toInt()
        userInfo.sex =
            if (MrkScaleManager.getScaleUserBean().sex == 1) ICConstant.ICSexType.ICSexTypeMale else ICConstant.ICSexType.ICSexTypeFemal
        ICDeviceManager.shared().updateUserInfo(userInfo)
        val reCalcWeightData =
            ICDeviceManager.shared().bodyFatAlgorithmsManager.reCalcBodyFatWithWeightData(
                weightData,
                userInfo
            )

        log("锁定数据 $reCalcWeightData")
        val bean = formatScaleData(reCalcWeightData)

        if (resetScaleListener == null) {
            MrkScaleManager.getListenerManager()
                .forEachListener {
                    it.onDataSuccess(
                        numberFormat(reCalcWeightData.weight_kg),
                        formatScaleData(reCalcWeightData)
                    )
                    it.onDeviceScaleMeasureStatus(DeviceScaleMeasureEnum.MEASURE_END)
                }
        } else {
            resetScaleListener.invoke(bean)
        }
    }

}