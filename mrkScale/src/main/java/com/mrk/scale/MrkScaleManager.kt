package com.mrk.scale

import android.content.Context
import androidx.lifecycle.Lifecycle
import com.mrk.scale.bean.DeviceScaleBrandEnum
import com.mrk.scale.bean.DeviceScaleConnectEnum
import com.mrk.scale.bean.DeviceScaleDataBean
import com.mrk.scale.bean.DeviceScaleSearchBean
import com.mrk.scale.bean.DeviceScaleTypeEnum
import com.mrk.scale.bean.DeviceScaleUserBean
import com.v.log.LogConfig
import com.v.log.VLog

/**
 * author  : ww
 * desc    : 体脂称管理类
 * time    : 2024/04/07 15:56
 */
object MrkScaleManager {


    private var function: DeviceScaleBaseFunction? = null

    private var scaleUserBean = DeviceScaleUserBean(height = 172.0, sex = 1)

    private val listenerManager = DeviceScaleListenerManager()

    /**
     * 获取所有监听
     */
    fun getListenerManager(): DeviceScaleListenerManager {
        return listenerManager
    }

    /**
     * 初始化
     * @param showLog  是否打开日志
     */
    fun init(context: Context, showLog: Boolean) {
        VLog.init(LogConfig(context, showLog, true))
    }


    /**
     * 初始化
     * @param communicationProtocol 通过接口返回
     * @param communicationProtocol  DeviceConstants.D_SERVICE_SCALE_WL 沃来体重秤
     * @param communicationProtocol  DeviceConstants.D_SERVICE_SCALE_LF 乐福体重秤
     */
    fun create(context: Context, communicationProtocol: Int) {
        val type = getDeviceBrand(communicationProtocol)
        if (type == DeviceScaleBrandEnum.BRAND_WL) {
            if (function is DeviceScaleWl) {
                return
            }
            function?.clear()
            function = DeviceScaleWl()
            function?.init(context)
        } else if (type == DeviceScaleBrandEnum.BRAND_LF) {
            if (function is DeviceScaleLf) {
                return
            }
            function?.clear()
            function = DeviceScaleLf()
            function?.init(context)
        }
    }

    /**
     * 设置监听
     */
    fun addScaleListener(owner: Lifecycle?, listener: DeviceScaleListener): MrkScaleManager {
        listenerManager.addListener(owner, listener)
        return this
    }

    /**
     * 移除监听器逻辑
     */
    fun removeScaleListener(listener: DeviceScaleListener) {
        listenerManager.removeListener(listener)
    }

    /**
     *  开始搜索
     */
    fun startSearch() {
        function?.startSearch()
    }

    /**
     * 停止搜索
     */
    fun stopSearch() {
        function?.stopSearch()
    }

    /**
     * 连接
     */
    fun connect(bean: DeviceScaleSearchBean) {
        function?.connect(bean)
    }

    /**
     * 连接
     * @param deviceMac 蓝牙名称
     * @param deviceMac 设备蓝牙mac地址l
     * @param  typeEnum DeviceScaleTypeEnum 设备电级
     */
    fun connect(
        deviceName: String,
        deviceMac: String,
        typeEnum: DeviceScaleTypeEnum,//电极类型 4=四电极 8=八电极

    ) {
        function?.run {
            connect(
                DeviceScaleSearchBean(
                    deviceName = deviceName,
                    deviceMac = deviceMac,
                    brandEnum = this.brandEnum,
                    typeEnum = typeEnum,
                    connectEnum = DeviceScaleConnectEnum.OFF
                )
            )
        }
    }

    /**
     * 断开连接
     */
    fun disConnect() {
        function?.disConnect()
    }

    /**
     * 判断是否连接成功
     */
    fun isConnect(): Boolean {
        return function?.isConnect()!!
    }

    /**
     * 清除数据
     */
    fun clear(isClearListener: Boolean = true) {
        function?.clear()
        function = null
        if (isClearListener) {
            listenerManager.clear()
        }
    }

    /**
     * @param scaleUserBean 为空会拿之前设置过的,如果直接也没设置过,会拿默认的
     * 设置用户信息重新计算
     */
    fun resetScale(
        scaleUserBean: DeviceScaleUserBean? = null,
        resetScaleListener: ((DeviceScaleDataBean) -> Unit)? = null
    ) {
        if (scaleUserBean != null) {
            setScaleUserBean(scaleUserBean)
        }
        function?.resetScale(resetScaleListener)
    }


    /**
     * 设置用户信息
     */
    fun setScaleUserBean(scaleUserBean: DeviceScaleUserBean) {
        this.scaleUserBean = scaleUserBean
    }

    /**
     * 获取设置的用户信息
     */
    fun getScaleUserBean(): DeviceScaleUserBean {
        return this.scaleUserBean
    }

    /**
     * 通过协议类型获取品牌枚举
     */
    fun getDeviceBrand(communicationProtocol: Int): DeviceScaleBrandEnum {
        if (communicationProtocol == DeviceScaleConstants.D_SERVICE_SCALE_WL) {
            return DeviceScaleBrandEnum.BRAND_WL
        } else if (communicationProtocol == DeviceScaleConstants.D_SERVICE_SCALE_LF) {
            return DeviceScaleBrandEnum.BRAND_LF
        } else {
            throw NullPointerException("不支持的体重秤协议")
        }
//        ppbluekit
    }
}