package com.mrk.scale

import android.content.Context
import android.util.Log
import cn.icomon.icdevicemanager.model.data.ICWeightData
import com.mrk.scale.bean.DeviceScaleBrandEnum
import com.mrk.scale.bean.DeviceScaleConnectEnum
import com.mrk.scale.bean.DeviceScaleDataBean
import com.mrk.scale.bean.DeviceScaleSearchBean
import com.mrk.scale.bean.toScaleDataBean
import com.peng.ppscale.vo.PPBodyFatModel
import com.v.log.util.logD
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * author  : ww
 * desc    :
 * time    : 2024/04/07 14:56
 */
abstract class DeviceScaleBaseFunction {

    val TAG = "DeviceScaleBaseFunction"

    val uniqueData = mutableSetOf<String>()

    var brandEnum = DeviceScaleBrandEnum.BRAND_UNKNOWN

    var currentScaleBean: DeviceScaleSearchBean? = null


    open fun init(context: Context) {
        log("init")
    }

    open fun startSearch() {
        uniqueData.clear()
        log("startSearch")
    }

    open fun stopSearch() {
        log("stopSearch")
    }

    open fun connect(bean: DeviceScaleSearchBean) {
        bean.brandEnum = brandEnum
        this.currentScaleBean = bean
        setConnectStatus(DeviceScaleConnectEnum.ING)
    }


    open fun disConnect() {
        log("disConnect")
        setConnectStatus(DeviceScaleConnectEnum.OFF)
    }

    open fun clear() {
        currentScaleBean = null
        uniqueData.clear()
//        log("clear")
    }


    /**
     * 重新计算
     */
    open fun resetScale(resetScaleListener: ((DeviceScaleDataBean) -> Unit)? = null) {

    }

    fun log(msg: String) {
        "${brandEnum.name}  $msg".logD(TAG)
//        Log.d(TAG,"${brandEnum.name}  $msg")
    }


    /**
     * 设置当前对象的连接状态
     */
    fun setConnectStatus(status: DeviceScaleConnectEnum) {
        if (this.currentScaleBean == null) {
            return
        }
        this.currentScaleBean?.connectEnum = status

        log("ConnectStatus:${status}  $currentScaleBean")

        val mac = this.currentScaleBean?.deviceMac ?: ""
        MrkScaleManager.getListenerManager()
            .forEachListener {
                it.onConnectStatus(
                    status,
                    mac
                )
            }

    }


    /**
     * 判断体脂称是否连接成功
     */
    fun isConnect(): Boolean {
        if (currentScaleBean == null) {
            return false
        }
        return currentScaleBean!!.connectEnum == DeviceScaleConnectEnum.ON
    }


    /**
     * 乐福体重秤数据转换
     */
    fun formatScaleData(
        data: PPBodyFatModel
    ): DeviceScaleDataBean {
        return data.toScaleDataBean(currentScaleBean!!.typeEnum, MrkScaleManager.getScaleUserBean())
    }


    /**
     * 沃来体重秤数据转换
     */
    fun formatScaleData(
        data: ICWeightData
    ): DeviceScaleDataBean {
        return data.toScaleDataBean(MrkScaleManager.getScaleUserBean())
    }

    /**
     * 处理精度问题 只保留一位小数
     */
    fun numberFormat(num: Double): String {
        return String.format("%.1f", num)
    }


    /**
     * 通过传入生日得到年龄
     */
    fun calculateAge(): Int {
        if (MrkScaleManager.getScaleUserBean().birthday.isEmpty()) {
            return 20
        } else {
            try {
                val birthDate = MrkScaleManager.getScaleUserBean().birthday

                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val birthDateObj: Date = sdf.parse(birthDate) ?: return -1 // 返回 -1 表示日期格式错误

                val birthCalendar = Calendar.getInstance()
                birthCalendar.time = birthDateObj

                val todayCalendar = Calendar.getInstance()

                var age = todayCalendar.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR)
                if (todayCalendar.get(Calendar.DAY_OF_YEAR) < birthCalendar.get(Calendar.DAY_OF_YEAR)) {
                    age--
                }

                return if (age <= 0) {
                    18
                } else if (age >= 100) {
                    99
                } else {
                    age
                }
            } catch (e: Exception) {
                return 20
            }

        }


    }

}