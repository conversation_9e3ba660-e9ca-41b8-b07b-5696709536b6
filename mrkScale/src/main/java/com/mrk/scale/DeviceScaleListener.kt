package com.mrk.scale

import com.mrk.scale.bean.DeviceScaleConnectEnum
import com.mrk.scale.bean.DeviceScaleDataBean
import com.mrk.scale.bean.DeviceScaleMeasureEnum
import com.mrk.scale.bean.DeviceScaleSearchEnum
import com.mrk.scale.bean.DeviceScaleSearchBean

/**
 * author  : ww
 * desc    : 体脂称回调
 * time    : 2024/4/3 13:29
 */
interface DeviceScaleListener {

    /**
     * 体脂称搜索状态
     */
    fun onSearchStatus(status: DeviceScaleSearchEnum) {

    }

    /**
     * 测量过程
     */
    fun onDeviceScaleMeasureStatus(status: DeviceScaleMeasureEnum) {

    }

    /**
     * 体脂称连接状态
     */
    fun onConnectStatus(status: DeviceScaleConnectEnum, mac: String = "")
    {

    }

    /**
     * 搜索到的体脂称数据
     */
    fun onSearchData(bean: DeviceScaleSearchBean)
    {

    }

    /**
     * 称重过程数据
     * @param weightStr 体重
     */
    fun onDataProcess(
        weightStr: String
    )
    {

    }

    /**
     * 称重成功数据
     * @param weightStr 体重
     * @param bean 封装好直接给后台的数据
     */
    fun onDataSuccess(
        weightStr: String,
        bean: DeviceScaleDataBean
    )
    {

    }


    /**
     * 超重
     */
    fun onMonitorOverWeight() {

    }

}