package com.mrk.scale.bean

import cn.icomon.icdevicemanager.model.data.ICWeightData
import com.mrk.scale.bean.DeviceScaleTypeEnum
import com.mrk.scale.bean.DeviceScaleUserBean
import com.peng.ppscale.vo.PPBodyFatModel

/**
 * author  : ww
 * desc    : 后端需要的数据
 * time    : 2024/4/18 15:49
 */
data class DeviceScaleDataBean(
    var scaleUserId: String, // 体脂秤用户ID
    var insertType: Int = 1, // 1.自动，2 手动
    var modelId: String = "", // 型号ID
    var electrodeType: Int = 1, // 电极类型 1-4电极，2-8电极
    var score: Int = 0, // 身体得分（健康评分）
    var weight: String = "", // 重量（kg）
    var height: String = "", // 身高
    var bmi: String = "", // BMI
    var bodyAge: Int = 0, // 身体年龄
    var bodyFatRate: String = "", // 体脂率（脂肪率）
    var muscle: String = "", // 肌肉（肌肉量）（kg）
    var leanBodyMass: String = "", // 去脂体重（kg）
    var subcutaneousFat: String = "", // 皮下脂肪率
    var visceralFat: String = "", // 内脏脂肪等级
    var basalMetabolism: String = "", // 基础代谢
    var skeletalMuscleRatio: String = "", // 骨骼肌量
    var protein: String = "", // 蛋白质率
    var waterContent: String = "", // 体水分（体水分率）
    var bodyType: String = "", // 体型（身体类型）
    var bodyFatMass: String = "", // 体脂量（脂肪量）
    var muscleRate: String = "", // 肌肉率
    var suggestCalorieIntake: String = "", // 建议卡路里摄入
    var obesityLevel: String = "", // 肥胖水平（肥胖度）
    var idealBodyWeight: String = "", // 理想体重
    var controlBodyWeight: String = "", // 控制体重
    var waterMass: String = "", // 水分量
    var proteinMass: String = "", // 蛋白量（蛋白质量）
    var inorganicSaltMass: String = "", // 无机盐量
    var bodyCellMass: String = "", // 身体细胞量
    var extracellularWaterVolume: String = "", // 细胞外水量
    var intracellularWaterVolume: String = "", // 细胞内水量
    var subcutaneousFatMass: String = "", // 皮下脂肪量
    var skeletalMuscleRate: String = "", // 骨骼肌率
    var skeletalMuscleMassExponent: String = "", // 骨骼肌质量指数
    var fatControl: String = "", // 脂肪控制量
    var muscleControl: String = "", // 肌肉控制量
    var heartRate: String = "", // 心率
    var waistHipRate: String = "", // 推测腰臀比
    var boneMass: String = "", // 骨量
    var leftArmFatRate: String = "", // 左手脂肪率
    var rightArmFatRate: String = "", // 右手脂肪率
    var leftLegFatRate: String = "", // 左脚脂肪率
    var rightLegFatRate: String = "", // 右脚脂肪率
    var allBodyFatRate: String = "", // 躯干脂肪率
    var leftArmMuscle: String = "", // 左手肌肉量
    var leftArmMuscleRate: String = "", // 左手肌肉率
    var rightArmMuscle: String = "", // 右手肌肉
    var rightArmMuscleRate: String = "", // 右手肌肉率
    var leftLegMuscle: String = "", // 左脚肌肉
    var leftLegMuscleRate: String = "", // 左脚肌肉率
    var rightLegMuscle: String = "", // 右脚肌肉
    var rightLegMuscleRate: String = "", // 右脚肌肉率
    var trunkMuscle: String = "", // 躯干肌肉
    var trunkMuscleRate: String = "", // 躯干肌肉率
    var leftArmImpedance: String = "", // 左手阻抗
    var rightArmImpedance: String = "", // 右手阻抗
    var leftLegImpedance: String = "", // 左脚阻抗
    var rightLegImpedance: String = "", // 右脚阻抗
    var bodyImpedance: String = "", // 躯干阻抗
    var leftArmFat: String = "", // 左手脂肪
    var rightArmFat: String = "", // 右手脂肪
    var leftLegFat: String = "", // 左脚脂肪
    var rightLegFat: String = "", // 右脚脂肪
    var allBodyFat: String = "", // 躯干脂肪量
    var leftArmSkeletalMuscle: String = "", // 左手骨骼肌
    var rightArmSkeletalMuscle: String = "", // 右手骨骼肌
    var leftLegSkeletalMuscle: String = "", // 左脚骨骼肌
    var rightLegSkeletalMuscle: String = "", // 右脚骨骼肌
    var limbsSkeletalMuscle: String = "", // 四肢骨骼肌质量指数
    var fatBalance: String = "", // 脂肪均衡
    var muscleBalance: String = "", // 肌肉均衡
    var healthStandardInterval: HealthStandardInterval? = null,//状态区间数据
    var healthStandard: HealthStandard? = null,//状态区间数据
    var upData: String = ""//体脂秤原始数据
) {
    data class HealthStandardInterval(
        var weightInterval: String = "", // 体重标准区间
        var bmiInterval: String = "", // BMI标准区间
        var bodyFatMassInterval: String = "", // 脂肪量标准区间
        var bodyFatRateInterval: String = "", // 脂肪率标准区间
        var muscleRateInterval: String = "", // 肌肉率标准区间
        var muscleInterval: String = "", // 肌肉量标准区间
        var basalMetabolismInterval: String = "", // 基础代谢标准区间
        var heartRateInterval: String = "", // 心率标准区间
        var bodyAgeInterval: String = "", // 身体年龄标准区间
        var scoreInterval: String = "", // 健康评分标准区间
        var obesityLevelInterval: String = "", // 肥胖水平（肥胖度）标准区间
        var leanBodyMassInterval: String = "", // 去脂体重标准区间
        var waterMassInterval: String = "", // 体水分量标准区间
        var waterContentInterval: String = "", // 体水分率标准区间
        var proteinInterval: String = "", // 蛋白质率标准区间
        var proteinMassInterval: String = "", // 蛋白质量标准区间
        var inorganicSaltMassInterval: String = "", // 无机盐量标准区间
        var bodyCellMassInterval: String = "", // 身体细胞量标准区间
        var extracellularWaterVolumeInterval: String = "", // 细胞外水量标准区间
        var intracellularWaterVolumeInterval: String = "", // 细胞内水量标准区间
        var subcutaneousFatMassInterval: String = "", // 皮下脂肪量标准区间
        var subcutaneousFatInterval: String = "", // 皮下脂肪率标准区间
        var visceralFatInterval: String = "", // 内脏脂肪等级标准区间
        var boneMassInterval: String = "", // 骨量标准区间
        var skeletalMuscleRateInterval: String = "", // 骨骼肌率标准区间
        var skeletalMuscleRatioInterval: String = "", // 骨骼肌量标准区间
        var skeletalMuscleMassExponentInterval: String = "", // 骨骼肌质量指数标准区间
        var waistHipRateInterval: String = ""  // 腰臀比区间
    )

    data class HealthStandard(
        var leftArmMuscleStandard: String = "", /// 左上肢肌肉标准
        var rightArmMuscleStandard: String = "",///右上肢肌肉标准
        var leftLegMuscleStandard: String = "",///左下肢肌肉标准
        var rightLegMuscleStandard: String = "",///右下肢肌肉标准
        var trunkMuscleStandard: String = "",///躯干肌肉标准
        var leftArmFatStandard: String = "",///左上肢脂肪标准
        var rightArmFatStandard: String = "",///右上肢脂肪标准
        var leftLegFatStandard: String = "",///左下肢脂肪标准
        var rightLegFatStandard: String = "",///右下肢脂肪标准
        var allBodyFatStandard: String = "",///躯干脂肪量标准
    )
}

fun PPBodyFatModel.toScaleDataBean(
    typeEnum: DeviceScaleTypeEnum,
    userBean: DeviceScaleUserBean
): DeviceScaleDataBean {
    this.run {
        return DeviceScaleDataBean(
            scaleUserId = userBean.id,//体脂秤用户id
            insertType = 1,//1.自动，2 手动
            modelId = "",//型号ID
            electrodeType = if (typeEnum == DeviceScaleTypeEnum.TYPE_8) 2 else 1,
            score = ppBodyScore,//得分
            weight = ppWeightKg.toString(),//重量（kg）
            height = userBean.height.toString(),//身高
            bmi = ppBMI.toString(),//bmi
            bodyAge = ppBodyAge,//身体年龄
            bodyFatRate = ppFat.toString(),//体脂率（脂肪率）
            muscle = ppMuscleKg.toString(),//肌肉（肌肉量）（kg）
            leanBodyMass = ppLoseFatWeightKg.toString(),//去脂体重（kg））
            subcutaneousFat = ppBodyFatSubCutPercentage.toString(),//皮下脂肪率
            visceralFat = ppVisceralFat.toString(),//内脏脂肪等级
            basalMetabolism = ppBMR.toString(),//基础代谢
            skeletalMuscleRatio = ppBodySkeletalKg.toString(),//骨骼肌量
            protein = ppProteinPercentage.toString(),//蛋白质率
            waterContent = ppWaterPercentage.toString(),//水分（水分率）
            bodyType = ppBodyType?.type?.toString() ?: "", // 体型（身体类型）
            bodyFatMass = ppBodyfatKg.toString(), // 体脂量（脂肪量）
            muscleRate = ppMusclePercentage.toString(), // 肌肉率
            suggestCalorieIntake = ppDCI.toString(), // 建议卡路里摄入
            obesityLevel = ppObesity.toString(), // 肥胖水平（肥胖度）
            idealBodyWeight = ppIdealWeightKg.toString(), // 理想体重
            controlBodyWeight = ppControlWeightKg.toString(), // 控制体重
            waterMass = ppWaterKg.toString(), // 水分量
            proteinMass = ppProteinKg.toString(), // 蛋白量（蛋白质量）
            inorganicSaltMass = ppMineralKg.toString(), // 无机盐量
            bodyCellMass = ppCellMassKg.toString(), // 身体细胞量
            extracellularWaterVolume = ppWaterECWKg.toString(), // 细胞外水量
            intracellularWaterVolume = ppWaterICWKg.toString(), // 细胞内水量
            subcutaneousFatMass = ppBodyFatSubCutKg.toString(), // 皮下脂肪量
            skeletalMuscleRate = ppBodySkeletal.toString(), // 骨骼肌率
            skeletalMuscleMassExponent = ppSmi.toString(), // 骨骼肌质量指数
            fatControl = ppFatControlKg.toString(), // 脂肪控制量
            muscleControl = ppBodyMuscleControl.toString(), // 肌肉控制量
            heartRate = ppHeartRate.toString(), // 心率
            waistHipRate = ppWHR.toString(), // 推测腰臀比
            boneMass = ppBoneKg.toString(), // 骨量
            leftArmFatRate = ppBodyFatRateLeftArm.toString(), // 左手脂肪率
            rightArmFatRate = ppBodyFatRateRightArm.toString(), // 右手脂肪率
            leftLegFatRate = ppBodyFatRateLeftLeg.toString(), // 左脚脂肪率
            rightLegFatRate = ppBodyFatRateRightLeg.toString(), // 右脚脂肪率
            allBodyFatRate = ppBodyFatRateTrunk.toString(), // 躯干脂肪率
            leftArmMuscle = ppMuscleKgLeftArm.toString(), // 左手肌肉量
            leftArmMuscleRate = ppMuscleRateLeftArm.toString(),  // 左手肌肉率
            rightArmMuscle = ppMuscleKgRightArm.toString(), // 右手肌肉
            rightArmMuscleRate = ppMuscleRateRightArm.toString(), // 右手肌肉率
            leftLegMuscle = ppMuscleKgLeftLeg.toString(), // 左脚肌肉
            leftLegMuscleRate = ppMuscleRateLeftLeg.toString(), // 左脚肌肉率
            rightLegMuscle = ppMuscleKgRightLeg.toString(), // 右脚肌肉
            rightLegMuscleRate = ppMuscleRateRightLeg.toString(), // 右脚肌肉率
            trunkMuscle = ppMuscleKgTrunk.toString(), // 躯干肌肉
            trunkMuscleRate = ppMuscleRateTrunk.toString(), // 躯干肌肉率
            leftArmImpedance = ppBodyBaseModel?.z100KhzLeftArmDeCode?.toString() ?: "", // 左手阻抗
            rightArmImpedance = ppBodyBaseModel?.z100KhzRightArmDeCode?.toString()
                ?: "", // 右手阻抗
            leftLegImpedance = ppBodyBaseModel?.z100KhzLeftLegDeCode?.toString() ?: "", // 左脚阻抗
            rightLegImpedance = ppBodyBaseModel?.z100KhzRightLegDeCode?.toString()
                ?: "", // 右脚阻抗
            bodyImpedance = ppBodyBaseModel?.z100KhzTrunkDeCode?.toString() ?: "", // 躯干阻抗
            leftArmFat = ppBodyFatKgLeftArm.toString(), // 左手脂肪量
            rightArmFat = ppBodyFatKgRightArm.toString(), // 右手脂肪量
            leftLegFat = ppBodyFatKgLeftLeg.toString(), // 左脚脂肪量
            rightLegFat = ppBodyFatKgRightLeg.toString(), // 右脚脂肪量
            allBodyFat = ppBodyFatKgTrunk.toString(), // 躯干脂肪量
            leftArmSkeletalMuscle = "", // 左手骨骼肌
            rightArmSkeletalMuscle = "", // 右手骨骼肌
            leftLegSkeletalMuscle = "", // 左脚骨骼肌
            rightLegSkeletalMuscle = "", // 右脚骨骼肌
            limbsSkeletalMuscle = "", // 四肢骨骼肌质量指数
            fatBalance = ppBalanceFatArmLegLevel.toString(), // 脂肪均衡
            muscleBalance = ppBalanceArmLegLevel.toString(), // 肌肉均衡
            healthStandardInterval = DeviceScaleDataBean.HealthStandardInterval(
                weightInterval = ppWeightKgList?.toString() ?: "",                    // 体重标准区间
                bmiInterval = ppBMIList?.toString() ?: "",                            // bmi标准区间
                bodyFatMassInterval = ppBodyfatKgList?.toString() ?: "",              // 脂肪量标准区间
                bodyFatRateInterval = ppFatList?.toString() ?: "",                    // 脂肪率标准区间
                muscleRateInterval = ppMusclePercentageList?.toString() ?: "",        // 肌肉率标准区间
                muscleInterval = ppMuscleKgList?.toString() ?: "",                    // 肌肉量标准区间
                basalMetabolismInterval = ppBMRList?.toString() ?: "",                // 基础代谢标准区间
                heartRateInterval = ppHeartRateList?.toString() ?: "",                // 心率标准区间
                bodyAgeInterval = ppBodyAgeList?.toString() ?: "",                    // 身体年龄标准区间
                scoreInterval = ppBodyScoreList?.toString() ?: "",                    // 健康评分标准区间
                obesityLevelInterval = ppObesityList.toString(),               // 肥胖水平（肥胖度）标准区间
                leanBodyMassInterval = ppLoseFatWeightKgList?.toString() ?: "",       // 去脂体重标准区间
                waterMassInterval = ppWaterKgList?.toString() ?: "",               // 体水分量标准区间
                waterContentInterval = ppWaterPercentageList?.toString() ?: "",          // 体水分率标准区间
                proteinInterval = ppProteinPercentageList?.toString() ?: "",          // 蛋白质率标准区间
                proteinMassInterval = ppProteinKgList?.toString() ?: "",              // 蛋白质量标准区间
                inorganicSaltMassInterval = ppMineralKgList.toString(),        // 无机盐量标准区间
                bodyCellMassInterval = ppCellMassKgList.toString(),            // 身体细胞量标准区间
                extracellularWaterVolumeInterval = ppWaterECWKgList.toString(),// 细胞外水量标准区间
                intracellularWaterVolumeInterval = ppWaterICWKgList.toString(),// 细胞内水量标准区间
                subcutaneousFatMassInterval = ppBodyFatSubCutKgList?.toString() ?: "",// 皮下脂肪量标准区间
                subcutaneousFatInterval = ppBodyFatSubCutPercentageList?.toString()
                    ?: "",     // 皮下脂肪率标准区间
                visceralFatInterval = ppVisceralFatList?.toString() ?: "",            // 内脏脂肪等级
                boneMassInterval = ppBoneKgList?.toString() ?: "",                    // 骨量标准区间
                skeletalMuscleRateInterval = ppBodySkeletalList?.toString() ?: "",    // 骨骼肌率标准区间
                skeletalMuscleRatioInterval = ppBodySkeletalKgList?.toString() ?: "", // 骨骼肌量标准区间
                skeletalMuscleMassExponentInterval = ppSmiList.toString(),     // 骨骼肌质量指数标准区间
                waistHipRateInterval = ppWHRList.toString(),      // 腰臀比区间
            ),//状态区间数据
            healthStandard = DeviceScaleDataBean.HealthStandard(
                leftArmMuscleStandard = ppLeftArmMuscleLevel.toString(), /// 左上肢肌肉标准
                rightArmMuscleStandard = ppRightArmMuscleLevel.toString(),///右上肢肌肉标准
                leftLegMuscleStandard = ppLeftLegMuscleLevel.toString(),///左下肢肌肉标准
                rightLegMuscleStandard = ppRightLegMuscleLevel.toString(),///右下肢肌肉标准
                trunkMuscleStandard = ppTrunkMuscleLevel.toString(),///躯干肌肉标准
                leftArmFatStandard = ppLeftArmFatLevel.toString(),///左上肢脂肪标准
                rightArmFatStandard = ppRightArmFatLevel.toString(),///右上肢脂肪标准
                leftLegFatStandard = ppLeftLegFatLevel.toString(),///左下肢脂肪标准
                rightLegFatStandard = ppRightLegFatLevel.toString(),///右下肢脂肪标准
                allBodyFatStandard = ppTrunkFatLevel.toString(),///躯干脂肪量标准
            ),//状态数据
            upData = this.toString()
        )
    }
}


fun ICWeightData.toScaleDataBean(userBean: DeviceScaleUserBean): DeviceScaleDataBean {
    this.run {
        return DeviceScaleDataBean(
            insertType = 1,//1.自动，2 手动
            score = Math.round(bodyScore).toInt(),//得分
            weight = weight_kg.toString(),//重量（kg）
            height = userBean.height.toString(),//身高
            bmi = bmi.toString(),//bmi
            bodyAge = physicalAge.toInt(),//身体年龄
            bodyFatRate = bodyFatPercent.toString(),//体脂率
            muscle = musclePercent.toString(),//肌肉（kg）
            leanBodyMass = boneMass.toString(),//去脂体重（kg））
            subcutaneousFat = subcutaneousFatPercent.toString(),//皮下脂肪
            visceralFat = visceralFat.toString(),//内脏脂肪
            basalMetabolism = bmr.toString(),//基础代谢
            skeletalMuscleRatio = smPercent.toString(),//骨骼肌率
            protein = proteinPercent.toString(),//蛋白质
            waterContent = moisturePercent.toString(),//水分
            scaleUserId = userBean.id,//体脂秤用户id

        )
    }
}



