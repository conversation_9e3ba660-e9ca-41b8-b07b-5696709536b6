apply plugin: 'maven-publish'

def androidSourcesJarTask = task androidSourcesJar(type: Jar) {
    if (project.hasProperty("kotlin")) {
        from android.sourceSets.main.java.getSrcDirs()
    } else if (project.hasProperty("android")) {
        from android.sourceSets.main.java.sourceFiles
    } else {
        from sourceSets.main.allSource
    }
    classifier = 'sources'
}

afterEvaluate {
    publishing {
        publications {
            Production(MavenPublication) {
                from components.release
                groupId = "com.mrk.scale"
                artifactId = "mrkScale"
                version = MavenRun.mavenVersion

                if (MavenRun.isPushSource) {
                    artifact(androidSourcesJarTask)
                }
            }
        }

        repositories {
            maven {
                //生产
                url = MavenRun.url
                credentials {
                    username = MavenRun.userName
                    password = MavenRun.password
                }
            }

        }
    }
}
