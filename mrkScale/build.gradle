plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'maven-publish'
    id 'com.kezong.fat-aar'
}
apply from: "upload.gradle"

android {
    namespace 'com.mrk.scale'
    compileSdk AndroidVersions.targetSdkVersion

    defaultConfig {
        minSdk AndroidVersions.minSdkVersion
        targetSdk AndroidVersions.targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    packagingOptions {
        exclude 'AndroidManifest.xml'
    }
}
repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    compileOnly "androidx.lifecycle:lifecycle-runtime-ktx:${Dependencies.lifecycleRuntimeKtx}"
//lifecycleScope
    implementation "com.github.oooo7777777:Vlog:${Dependencies.log}"
    //不能使用embed直接编译,不然沃莱会报错(Failed resolution of: Lcom/icomon/logger/ICLogLevel;)
    embed(name: 'ICDeviceManager', ext: 'aar')
    embed(name: 'ppblutoothkit-3.5.9', ext: 'aar')
//    api files('libs/ICDeviceManager.aar')
//    api files('libs/ppblutoothkit-3.5.9.aar')
}

