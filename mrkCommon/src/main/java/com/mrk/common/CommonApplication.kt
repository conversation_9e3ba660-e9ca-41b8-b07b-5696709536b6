package com.mrk.common

import android.content.BroadcastReceiver
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import com.mrk.device.MrkDeviceManger
import com.v.base.VBApplication
import com.v.log.VLog

/**
 * author  : ww
 * desc    :
 * time    : 2023/10/20 10:18
 */
open class CommonApplication : VBApplication() {
    companion object {
        lateinit var instance: CommonApplication
    }

    override fun initData() {
        instance = this
        MrkDeviceManger.init(this, logConfig()?.showLog ?: false)
    }

    /**
     * 兼容 android 14 广播安全问题
     */
    override fun registerReceiver(receiver: BroadcastReceiver?, filter: IntentFilter?): Intent? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            super.registerReceiver(receiver, filter, RECEIVER_EXPORTED)
        } else {
            super.registerReceiver(receiver, filter)
        }
    }

}