plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id 'maven-publish'
    id 'com.kezong.fat-aar'

}
apply from: "upload.gradle"

android {
    compileSdk AndroidVersions.compileSdkVersion
    defaultConfig {
        minSdk AndroidVersions.minSdkVersion
        targetSdk AndroidVersions.targetSdkVersion
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        dataBinding = true
    }

}


dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    api project(':mrkBase')
    api project(':mrkNetwork')
    api project(':mrkDevice')


    api "com.guolindev.permissionx:permissionx:${Dependencies.permissionX}"//权限库

}