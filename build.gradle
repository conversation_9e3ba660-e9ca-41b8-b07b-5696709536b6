buildscript {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://jitpack.io' }
        google()
        jcenter()
        mavenCentral()

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${AndroidVersions.kotlinVersion}"
        classpath 'com.github.kezong:fat-aar:1.3.8'
    }
}

allprojects {
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
    }

    repositories {
        maven {
            url 'https://storage.googleapis.com/download.flutter.io'
        }
        maven {
            credentials {
                username = "650e5bf5a220ae99aea1309c"
                password = "]qL0i2kqEMrP"
            }
            url = uri("https://packages.aliyun.com/maven/repository/2160068-release-Q0kcTH/")
        }
        maven {
            credentials {
                username = "650e5bf5a220ae99aea1309c"
                password = "]qL0i2kqEMrP"
            }
            url = uri("https://packages.aliyun.com/maven/repository/2160068-snapshot-25MJti/")
        }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://jitpack.io' }
        google()
        jcenter()
        mavenCentral()

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}