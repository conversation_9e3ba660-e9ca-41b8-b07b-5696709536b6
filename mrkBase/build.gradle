plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id 'maven-publish'
    id 'com.kezong.fat-aar'
}
apply from: "upload.gradle"

android {
    compileSdk AndroidVersions.compileSdkVersion

    defaultConfig {
        minSdk AndroidVersions.minSdkVersion
        targetSdk AndroidVersions.targetSdkVersion
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        dataBinding = true
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    api "org.jetbrains.kotlin:kotlin-reflect:${AndroidVersions.kotlinVersion}"
    api "androidx.core:core-ktx:${Dependencies.coreKtx}"
    api "androidx.appcompat:appcompat:${Dependencies.appcompat}"
    api "androidx.lifecycle:lifecycle-viewmodel-ktx:${Dependencies.lifecycleViewModelKtx}"
    api "androidx.lifecycle:lifecycle-common-java8:${Dependencies.lifecycleCommonKtx}"
    api "androidx.lifecycle:lifecycle-runtime-ktx:${Dependencies.lifecycleRuntimeKtx}"
    api "androidx.constraintlayout:constraintlayout:2.1.4"
    api "org.jetbrains.kotlinx:kotlinx-coroutines-android:${Dependencies.coroutines}"


    api "com.google.android.material:material:${Dependencies.material}"
    api "androidx.recyclerview:recyclerview:1.3.2"

    //glide
    api 'jp.wasabeef:glide-transformations:4.3.0'//glide转换器
    api 'com.github.bumptech.glide:glide:4.15.1'//glide
    //fastjson
    api "com.alibaba:fastjson:1.2.83"
    //refresh-layout
    api"com.scwang.smart:refresh-layout-kernel:2.0.3"
    api "com.scwang.smart:refresh-header-classics:2.0.3"
    api 'com.scwang.smart:refresh-layout-horizontal:2.0.0'
    //BaseRecyclerViewAdapterHelper
//    api "com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.7"
    api "io.github.cymchad:BaseRecyclerViewAdapterHelper:3.0.14"
    //BackgroundLibrary  （通过标签直接生成shape，无需再写shape.xml）
    api "com.github.JavaNoober.BackgroundLibrary:libraryx:1.7.2"
    // 状态栏 https://github.com/gyf-dev/ImmersionBar
    api 'com.geyifeng.immersionbar:immersionbar:3.2.2'
    // 语种切换框架：https://github.com/getActivity/MultiLanguages
    api 'com.github.getActivity:MultiLanguages:8.0'
    //livedata防止数据倒灌
    api 'com.kunminx.arch:unpeek-livedata:7.8.0'
    //日志打印
    api "com.github.oooo7777777:Vlog:${Dependencies.log}"
    //toast
    api 'com.github.getActivity:Toaster:12.5'

    api 'com.github.fondesa:recycler-view-divider:3.6.0'
    api project(':mrkNetwork')


}

