<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".dialog.VBListDialog">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:orientation="vertical">


        <TextView
            android:visibility="gone"
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@android:color/white"
            android:gravity="center"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@+id/line"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="title" />

        <View
            android:visibility="gone"
            android:id="@+id/line"
            style="@style/vb_line_horizontal_bold"
            app:layout_constraintBottom_toTopOf="@+id/recyclerView"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/vb_color_background"
            android:fadingEdge="none"
            android:overScrollMode="never"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>