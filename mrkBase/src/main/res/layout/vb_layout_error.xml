<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:gravity="center"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/ivEmpty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:adjustViewBounds="true"
        android:src="@mipmap/vb_iv_data_error" />

    <TextView
        android:id="@+id/tvEmptyHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="@string/vb_string_loading_failed"
        android:textColor="#bfbfbf" />

</LinearLayout>

