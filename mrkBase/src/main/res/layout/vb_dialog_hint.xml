<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.v.base.dialog.VBHintDialog">

    <data>
        <variable
            name="v"
            type="com.v.base.dialog.VBHintDialog" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:id="@+id/layoutContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:orientation="vertical"
            app:bl_corners_radius="12dp"
            app:bl_solid_color="@android:color/white"
            tools:background="@android:color/white">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingLeft="15dp"
                    android:paddingTop="15dp"
                    android:paddingRight="15dp"
                    android:singleLine="true"
                    android:text="@string/vb_string_hint"
                    android:textColor="#616161"
                    android:textSize="14.5sp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tvTitle"
                    android:gravity="center"
                    android:lineSpacingExtra="3dp"
                    android:minHeight="100dp"
                    android:padding="15dp"
                    android:scrollbars="vertical"
                    android:textColor="#1C1A1A"
                    android:textSize="14.5sp"
                    tools:text="温馨提示" />

                <View
                    android:id="@+id/tvLine"
                    style="@style/vb_line_horizontal"
                    android:layout_below="@+id/tvContent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tvLine"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvLeft"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="15dp"
                        android:layout_gravity="center"
                        android:text="@string/vb_string_cancel"
                        android:textColor="#999999"
                        android:textSize="14sp"
                        android:visibility="gone"
                        app:vb_click="@{v}"
                        tools:visibility="visible" />

                    <View
                        android:id="@+id/baseViewWire"
                        style="@style/vb_line_vertical"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvRight"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:padding="15dp"
                        android:text="@string/vb_string_confirm"
                        android:textColor="#FF5369"
                        android:textSize="14sp"
                        app:vb_click="@{v}" />
                </LinearLayout>
            </RelativeLayout>

        </RelativeLayout>
    </RelativeLayout>
</layout>