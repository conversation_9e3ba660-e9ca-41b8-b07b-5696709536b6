<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/ivEmpty"
        android:src="@mipmap/vb_iv_data_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:adjustViewBounds="true" />

    <TextView
        android:id="@+id/tvEmptyHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="@string/vb_string_temporarily_no_data"
        android:textColor="#bfbfbf" />

</LinearLayout>

