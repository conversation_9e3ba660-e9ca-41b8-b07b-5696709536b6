<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.v.base.VBTitleBar">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivStatusBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white">

            <TextView
                android:layout_marginStart="90dp"
                android:layout_marginEnd="90dp"
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                tools:text="title" />

            <FrameLayout
                android:id="@+id/flLeft"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="20dp"
                android:paddingRight="20dp">

                <TextView
                    android:id="@+id/tvLeft"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    tools:text="忽略全部" />

                <ImageView
                    android:id="@+id/ivLeft"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left|center"
                    android:adjustViewBounds="true"
                    android:src="@mipmap/vb_ic_back_black" />

            </FrameLayout>


            <FrameLayout
                android:id="@+id/flRight"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right"
                android:paddingLeft="20dp"
                android:paddingRight="20dp">

                <TextView
                    android:id="@+id/tvRight"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="right"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    tools:text="忽略全部" />

                <ImageView
                    android:id="@+id/ivRight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right|center"
                    android:gravity="center"
                    android:visibility="gone"
                    tools:src="@mipmap/vb_ic_back_black"
                    tools:visibility="visible" />

            </FrameLayout>


        </FrameLayout>

        <ImageView
            android:id="@+id/ivLine"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#F3F3F5"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

</layout>