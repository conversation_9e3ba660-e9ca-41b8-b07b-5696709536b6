package com.v.base.utils


import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.annotation.ColorInt
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.BaseRequestOptions
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.v.base.VBConfig
import jp.wasabeef.glide.transformations.CropCircleWithBorderTransformation
import jp.wasabeef.glide.transformations.RoundedCornersTransformation


/**
 * 加载图片
 * @param any 图片资源Glide所支持的
 * @param roundingRadius 图片圆角角度
 * @param errorResId 加载错误占位图
 */
fun ImageView.vbLoad(
    any: Any,
    roundingRadius: Int = 0,
    errorResId: Int = VBConfig.options.errorResId,
) = loadDispose(this, any, roundingRadius, errorResId, null)


/**
 * 加载不同圆角图片
 * @param any 图片资源Glide所支持的
 * @param topLeft 顶部左边圆角
 * @param topRight 顶部右边圆角
 * @param bottomLeft 底部左边圆角
 * @param bottomRight 底部右边圆角
 * @param errorResId 加载错误占位图
 */
fun ImageView.vbLoadRounded(
    any: Any,
    topLeft: Int = 0,
    topRight: Int = 0,
    bottomLeft: Int = 0,
    bottomRight: Int = 0,
    errorResId: Int = VBConfig.options.errorResId,
) {

    //顶部左边圆角
    val tfTopLeft =
        RoundedCornersTransformation(
            topLeft.vbDp2px2Int(),
            0,
            RoundedCornersTransformation.CornerType.TOP_LEFT
        )

    //顶部右边圆角
    val tfTopRight =
        RoundedCornersTransformation(
            topRight.vbDp2px2Int(),
            0,
            RoundedCornersTransformation.CornerType.TOP_RIGHT
        )

    //底部左边圆角
    val tfBottomLeft =
        RoundedCornersTransformation(
            bottomLeft.vbDp2px2Int(),
            0,
            RoundedCornersTransformation.CornerType.BOTTOM_LEFT
        )

    //底部右边圆角
    val tfBottomRight =
        RoundedCornersTransformation(
            bottomRight.vbDp2px2Int(),
            0,
            RoundedCornersTransformation.CornerType.BOTTOM_RIGHT
        )

    val tf =
        MultiTransformation(
            CenterCrop(), tfTopLeft, tfTopRight, tfBottomLeft, tfBottomRight
        )

    loadDispose(this, any, 0, errorResId, tf)
}


/**
 * 加载圆形图片
 * @param any 图片资源Glide所支持的
 * @param errorResId 加载错误占位图
 * @param borderSize 边框大小
 * @param borderColor 边框颜色
 */
fun ImageView.vbLoadCircle(
    any: Any,
    errorResId: Int = VBConfig.options.errorResId,
    borderSize: Int = 0,
    @ColorInt borderColor: Int = Color.WHITE,
) =
    loadDispose(this, any, -1, errorResId, null, borderSize, borderColor)


/**
 * 加载图片监听
 * @param any 图片资源Glide所支持的
 * @param w 图片设置宽度
 * @param h 图片设置高度
 * @param success 图片加载成功
 * @param error 图片加载失败
 */
fun Context.vbLoadListener(
    any: Any?,
    w: Int = 0,
    h: Int = 0,
    success: ((Drawable) -> Unit),
    error: (() -> Unit)? = null,
) = run {

    if (any == null) {
        error?.invoke()
    } else {
        Glide.with(this)
            .asDrawable().apply {
                if (w > 0 || h > 0) {
                    override(w, h)
                }
            }
            .load(any)
            .into(object : CustomTarget<Drawable>() {

                override fun onResourceReady(
                    resource: Drawable,
                    transition: Transition<in Drawable?>?,
                ) {
                    success.invoke(resource)
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    error?.invoke()
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                }
            })
    }


}

/**
 * 图片的处理
 */
@SuppressLint("CheckResult")
private fun loadDispose(
    image: ImageView,
    any: Any,
    roundingRadius: Int,
    errorResId: Int,
    transformation: Transformation<Bitmap>? = null,
    borderSize: Int = 0,
    @ColorInt borderColor: Int = Color.WHITE,
) {
    image.run {
        try {
            val options = RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.ALL)

            //不同圆角
            if (transformation != null) {
                options.transform(transformation)
            } else {
                //圆角
                if (roundingRadius > 0) {
                    val radius = roundingRadius.vbDp2px2Int()
                    options.transform(
                        CenterCrop(),
                        RoundedCornersTransformation(
                            radius, 0,
                            RoundedCornersTransformation.CornerType.ALL,
                        )
                    )
                } else if (roundingRadius < 0) {
                    if (borderSize > 0) {
                        options.transform(
                            CenterCrop(),
                            RoundedCornersTransformation(1280, 0),
                            CropCircleWithBorderTransformation(
                                borderSize.vbDp2px2Int(),
                                borderColor
                            )
                        ) //圆形
                    } else {
                        options.transform(CenterCrop(), RoundedCornersTransformation(1280, 0)) //圆形
                    }
                }
            }

            Glide.with(this.context)
                .load(any)
                .apply(options)
                .apply {
                    if (errorResId != 0) {
                        error(
                            loadError(
                                <EMAIL>,
                                errorResId,
                                options
                            )
                        )
                    }
                }
                .into(this)

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}


/**
 * 对加载错误的占位图的处理成圆角
 */
private fun loadError(
    context: Context,
    placeholderId: Int,
    options: BaseRequestOptions<*>,
): RequestBuilder<Drawable?>? {
    return Glide.with(context)
        .load(placeholderId)
        .apply(options)
}










