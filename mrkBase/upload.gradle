apply plugin: 'maven-publish'
apply plugin: 'kotlin-android'

def androidSourcesJarTask = task androidSourcesJar(type: Jar) {
    // 如果有Kotlin那么就需要打入dir : getSrcDirs
    if (project.hasProperty("kotlin")) {
        from android.sourceSets.main.java.getSrcDirs()
    } else if (project.hasProperty("android")) {
        from android.sourceSets.main.java.sourceFiles
    } else {
        from sourceSets.main.allSource
    }
    classifier = 'sources'
}


afterEvaluate {
    publishing {
        publications {
            Production(MavenPublication) {
                from components.release
                groupId = "com.mrk.base"
                artifactId = "mrkBase"
                //版本号,根据自己实际情况填写
                version = MavenRun.mavenVersion
                // 添加源代码构件
                if (MavenRun.isPushSource) {
                    artifact(androidSourcesJarTask)
                }
            }
        }

        repositories {
            maven {
                //生产
                url = MavenRun.url
                credentials {
                    username = MavenRun.userName
                    password =  MavenRun.password
                }
            }

        }
    }
}
