<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.v.demo">

    <uses-permission android:name="android.permission.INTERNET" />
    <!--    蓝牙所需权限-->
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"
        tools:node="replace" />
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        tools:node="replace" />

    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <!-- Android 12，
   申请权限要么加上 Manifest.permission.ACCESS_FINE_LOCATION // 添加这行确保所有情况下蓝牙扫描都能正常工作
   要么在不申请定位权限时,必须加上android:usesPermissionFlags="neverForLocation"，否则搜不到设备 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <!--    蓝牙所需权限-->

    <application
        android:name=".DemoApplication"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/dm_app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:resizeableActivity="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/vb_app_theme"
        tools:replace="android:label">
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".vb.ItemDecorationDemoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".scale.ScaleDemoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".vb.NetworkTestActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".device.DeviceMyBindActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".device.DeviceDetailsActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".vb.LanguageTestActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".vb.FragmentTestActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".push.Push3Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".push.Push1Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".push.Push2Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".device.DeviceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".VbDemoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".MainActivity"
            android:screenOrientation="portrait" />
    </application>

</manifest>