<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.vb.ThreeFragment" />

        <variable
            name="content"
            type="String" />

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <TextView
                    android:id="@+id/tvContent"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:background="@android:color/holo_blue_light"
                    android:gravity="center"
                    android:textColor="@android:color/white" />


                <Button
                    android:id="@+id/bt0"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="网络请求"
                    android:textAllCaps="false"
                    app:vb_click_animator_off="@{v}" />

                <Button
                    android:id="@+id/bt1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/string_countdown"
                    android:textAllCaps="false"
                    app:vb_click_animator_off="@{v}" />

                <Button
                    android:id="@+id/bt2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/string_heartbeat"
                    android:textAllCaps="false"
                    app:vb_click_animator_on="@{v}" />


                <Button
                    android:id="@+id/bt4"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/string_dialog"
                    android:textAllCaps="false"
                    app:vb_click="@{v}" />

                <Button
                    android:id="@+id/bt5"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/string_list_dialog"
                    android:textAllCaps="false"
                    app:vb_click="@{v}" />


                <Button
                    android:id="@+id/bt6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="多语言测试"
                    android:textAllCaps="false"
                    app:vb_click="@{v}" />


                <Button
                    android:id="@+id/bt7"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Fragment测试"
                    android:textAllCaps="false"
                    app:vb_click="@{v}" />


                <Button
                    android:id="@+id/bt8"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="IconFont"
                    android:textAllCaps="false"
                    app:vb_click="@{v}" />


                <TextView
                    android:id="@+id/tvIconFont"
                    vb_icon_font="@{`&#xe633;`}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="50sp" />


                <Button
                    android:id="@+id/bt9"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="RecyclerView ItemDecoration"
                    android:textAllCaps="false"
                    app:vb_click="@{v}" />
            </LinearLayout>
        </ScrollView>
    </FrameLayout>
</layout>