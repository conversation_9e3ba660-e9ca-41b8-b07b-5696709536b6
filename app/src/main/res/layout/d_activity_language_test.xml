<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".vb.LanguageTestActivity">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.vb.LanguageTestActivity" />

    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <Button
            android:id="@+id/bt6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-中文"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/bt7"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-英文"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/bt8"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-日文"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/bt9"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-法语"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/bt10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-意大利语"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/bt11"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-德语"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/bt12"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-西班牙语"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/bt13"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-跟随系统"
            android:textAllCaps="false"
            app:vb_click="@{v}" />


        <Button
            android:id="@+id/bt14"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="语言切换-韩语(没有配置这个语言)"
            android:textAllCaps="false"
            app:vb_click="@{v}" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="当前 Activity 语种："
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/tvLanguageActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:textColor="@android:color/black" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"

                android:layout_height="wrap_content"
                android:text="当前 Application 语种："
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/tvLanguageApplication"

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:textColor="@android:color/black" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="当前 System 语种："
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/tvLanguageSystem"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:textColor="@android:color/black" />
        </LinearLayout>
    </LinearLayout>

</layout>