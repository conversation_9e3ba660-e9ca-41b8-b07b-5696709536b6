<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".device.DeviceDetailsActivity">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.device.DeviceDetailsActivity" />

    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvConnectInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="连接设备信息:"
                android:textColor="@android:color/holo_orange_dark" />

            <TextView
                android:id="@+id/tvConnectStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="连接状态:"
                android:textColor="@android:color/holo_orange_dark" />


            <TextView
                android:id="@+id/tvDeviceStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设备状态:"
                android:textColor="@android:color/holo_orange_dark" />

            <TextView
                android:id="@+id/tvContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="数据回调:"
                android:textColor="@android:color/holo_orange_dark" />


            <Button
                android:id="@+id/btConnect"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="连接设备" />

            <Button
                android:id="@+id/btDisConnect"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="断开设备" />

            <Button
                android:id="@+id/btUnBind"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="解绑设备" />

            <Button
                android:id="@+id/btStart"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="启动设备" />

            <Button
                android:id="@+id/btPause"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="暂停设备" />


            <Button
                android:id="@+id/btDataClear"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设备数据清零" />

            <Button
                android:id="@+id/btRegisterNotify"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="开启数据回调" />

            <Button
                android:id="@+id/btUnRegisterNotify"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="关闭数据回调" />


            <Button
                android:id="@+id/btOta"
                vb_click="@{v}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="OTA升级" />

            <TextView
                android:id="@+id/tvOtaContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="ota回调:"
                android:textColor="@android:color/holo_orange_dark" />

            <ProgressBar
                android:id="@+id/otaProgressBar"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:max="100" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:background="@android:color/black"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvResistance"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@android:color/holo_blue_light"
                    android:gravity="center"
                    android:text="当前阻力:1"
                    android:textColor="@android:color/black" />

                <EditText
                    android:id="@+id/etResistance"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/shape_edit_bg"
                    android:gravity="center"
                    android:hint="输入阻力数值"
                    android:inputType="number"
                    android:text="1"
                    android:textColor="@android:color/white" />


                <Button
                    android:id="@+id/btResistance"
                    vb_click="@{v}"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="发送阻力" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="5dp"
                android:background="@android:color/black"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvSpeed"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@android:color/holo_blue_light"
                    android:gravity="center"
                    android:paddingStart="5dp"
                    android:text="当前速度:1"
                    android:textColor="@android:color/black" />

                <EditText
                    android:id="@+id/etSpeed"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/shape_edit_bg"
                    android:gravity="center"
                    android:hint="输入速度数值"
                    android:inputType="numberDecimal"
                    android:text="1"
                    android:textColor="@android:color/white" />

                <Button
                    android:id="@+id/btSpeed"
                    vb_click="@{v}"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="发送速度" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="5dp"
                android:background="@android:color/black"
                android:gravity="center"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btSpeedReduce"
                    vb_click="@{v}"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="速度-" />

                <EditText
                    android:id="@+id/etSpeedDecimal"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/shape_edit_bg"
                    android:gravity="center"
                    android:hint="速度每次增加"
                    android:inputType="numberDecimal"
                    android:textColor="@android:color/white" />

                <Button
                    android:id="@+id/btSpeedAdd"
                    vb_click="@{v}"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="速度+" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="5dp"
                android:background="@android:color/black"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvSlope"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@android:color/holo_blue_light"
                    android:gravity="center"
                    android:paddingStart="5dp"
                    android:text="当前坡度:1"
                    android:textColor="@android:color/black" />

                <EditText
                    android:id="@+id/etSlope"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_edit_bg"
                    android:gravity="center"
                    android:hint="输入坡度数值"
                    android:inputType="numberSigned"
                    android:text="1"
                    android:textColor="@android:color/white" />

                <Button
                    android:id="@+id/btSlope"
                    vb_click="@{v}"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="发送坡度" />

            </LinearLayout>


        </LinearLayout>

    </ScrollView>

</layout>