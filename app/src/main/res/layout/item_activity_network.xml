<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".adapter.NetworkActivityAdapter">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="bean"
            type="com.v.demo.bean.NetworkBean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:vb_img_url="@{bean.url}" />

    </LinearLayout>

</layout>