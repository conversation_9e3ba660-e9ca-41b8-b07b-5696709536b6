<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".vb.ItemDecorationDemoActivity">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.vb.ItemDecorationDemoActivity" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvVertical"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@android:color/holo_blue_dark"
            android:gravity="center"
            android:text="纵向布局"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/tvHorizontal"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@android:color/holo_blue_dark"
            android:gravity="center"
            android:text="横向布局"
            android:textColor="@android:color/black" />


        <TextView
            android:id="@+id/tvGrid"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@android:color/holo_blue_dark"
            android:gravity="center"
            android:text="网格布局"
            android:textColor="@android:color/black" />


        <TextView
            android:id="@+id/tvStaggeredGrid"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@android:color/holo_blue_dark"
            android:gravity="center"
            android:text="瀑布流布局"
            android:textColor="@android:color/black" />


        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:listitem="@layout/d_activity_item_decoration_demo_item" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </LinearLayout>

</layout>