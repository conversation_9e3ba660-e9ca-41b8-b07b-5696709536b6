<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".vb.NetworkTestActivity">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.vb.NetworkTestActivity" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <Button
            android:id="@+id/bt1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="ViewModel请求"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/bt2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="ViewModel串行请求"
            android:textAllCaps="false"
            app:vb_click="@{v}" />


        <Button
            android:id="@+id/bt3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="非ViewModel请求"
            android:textAllCaps="false"
            app:vb_click="@{v}" />


        <Button
            android:id="@+id/bt4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="非ViewModel串行请求"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

    </LinearLayout>

</layout>