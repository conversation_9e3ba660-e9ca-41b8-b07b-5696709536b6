<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".VbDemoActivity">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.VbDemoActivity" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />


        <net.lucode.hackware.magicindicator.MagicIndicator
            android:id="@+id/magicIndicator"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_gravity="bottom"
            android:background="@android:color/white" />

    </LinearLayout>
</layout>