<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".adapter.DeviceMyBindAdapter">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="bean"
            type="com.mrk.device.bean.DeviceMyBindBean.Record" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/vb_color_background"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:background="@android:color/white"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="10dp">

            <ImageView
                vb_img_url="@{bean.cover}"
                android:layout_width="80dp"
                android:layout_height="80dp"
                tools:background="@android:color/holo_red_dark" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingHorizontal="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@{bean.modelName}"
                    android:textColor="@android:color/black"
                    tools:text="name" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@{bean.bluetoothName}"
                    android:textColor="@android:color/black"
                    tools:text="name" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@{bean.mac}"
                    android:textColor="@android:color/black"
                    tools:text="mac" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="70dp"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvConnect"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="@android:color/holo_blue_light"
                    android:gravity="center"
                    android:text="连接"
                    android:textColor="@android:color/white" />

                <TextView
                    android:id="@+id/tvUnBindDevice"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="10dp"
                    android:layout_weight="1"
                    android:background="@android:color/holo_blue_light"
                    android:gravity="center"
                    android:text="解绑"
                    android:textColor="@android:color/white" />


            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</layout>