<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="v"
            type="com.v.demo.MainActivity" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <Button
            android:id="@+id/btVbDemo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Vb库Demo"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/btDevice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="设备demo"
            android:textAllCaps="false"
            app:vb_click="@{v}" />

        <Button
            android:id="@+id/btReport"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="埋点demo"
            android:textAllCaps="false"
            app:vb_click="@{v}" />


        <Button
            android:id="@+id/btScaleDemo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="体脂称Demo"
            android:textAllCaps="false"
            app:vb_click="@{v}" />
    </LinearLayout>
</layout>
