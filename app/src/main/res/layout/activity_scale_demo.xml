<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".scale.ScaleDemoActivity">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.scale.ScaleDemoActivity" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="150dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvBattery"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:textColor="@android:color/black"
                    tools:text="电量:" />

                <TextView
                    android:id="@+id/tvStep"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:textColor="@android:color/black"
                    tools:text="tvInfo" />

                <TextView
                    android:id="@+id/tvInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:textColor="@android:color/black"
                    tools:text="tvInfo" />

            </LinearLayout>
        </ScrollView>


        <Button
            android:id="@+id/btSwitchScale"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="体脂称:乐福"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/btConnect"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="直接连接:沃来 AAA049"
            android:textColor="@android:color/white" />


        <Button
            android:id="@+id/btStartScanDevice"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="开始搜索"
            android:textColor="@android:color/white" />


        <Button
            android:id="@+id/btStopScanDevice"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="停止搜索"
            android:textColor="@android:color/white" />


        <Button
            android:id="@+id/btSetUser"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="设置用户信息"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/btResetScale"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="重新计算"
            android:textColor="@android:color/white" />


        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:listitem="@layout/activity_scale_demo_item" />
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </LinearLayout>
</layout>
