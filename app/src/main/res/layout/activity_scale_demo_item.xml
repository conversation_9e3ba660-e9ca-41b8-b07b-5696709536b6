<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".scale.ScaleDemoAdapter"
    xmlns:tools="http://schemas.android.com/tools">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <ImageView
            android:id="@+id/ivCover"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@android:color/holo_red_dark" />


        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toEndOf="@+id/ivCover"
            app:layout_constraintTop_toTopOf="@+id/ivCover"
            tools:text="tvName" />

        <TextView
            android:id="@+id/tvBluetoothName"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@android:color/black"
            app:layout_constraintBottom_toBottomOf="@+id/ivCover"
            app:layout_constraintStart_toEndOf="@+id/ivCover"
            app:layout_constraintTop_toTopOf="@+id/ivCover"
            tools:text="tvBluetoothName" />


        <TextView
            android:id="@+id/tvMac"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@android:color/black"
            app:layout_constraintBottom_toBottomOf="@+id/ivCover"
            app:layout_constraintStart_toEndOf="@+id/ivCover"
            tools:text="tvMac" />


        <TextView
            android:id="@+id/tvConnect"
            android:layout_width="70dp"
            android:layout_height="0dp"
            android:layout_marginEnd="10dp"
            android:background="@android:color/holo_blue_light"
            android:gravity="center"
            android:text="连接"
            android:textColor="@android:color/white"
            app:layout_constraintBottom_toBottomOf="@+id/ivCover"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivCover" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>