<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    tools:context=".device.DeviceActivity">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.device.DeviceActivity" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:id="@+id/tvBluetoothStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:text="蓝牙状态:"
            android:textColor="@android:color/holo_orange_dark" />


        <TextView
            android:id="@+id/tvSearchStart"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="搜索设备"
            android:textColor="@android:color/white" />

        <TextView
            android:id="@+id/tvSearchStop"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="20dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="停止搜索"
            android:textColor="@android:color/white" />


        <TextView
            android:id="@+id/tvMyDevice"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="20dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="我绑定的设备"
            android:textColor="@android:color/white" />


        <TextView
            android:id="@+id/tvDeviceDetails"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="20dp"
            android:background="@android:color/black"
            android:gravity="center"
            android:text="模拟进入训练"
            android:textColor="@android:color/white" />


        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:listitem="@layout/activity_device_details_item" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </LinearLayout>

</layout>