<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".vb.FragmentTestActivity">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="v"
            type="com.v.demo.vb.FragmentTestActivity" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">



        <Button
            android:id="@+id/bt1"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="添加"
            android:textAllCaps="false" />


        <Button
            android:id="@+id/bt2"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="显示"
            android:textAllCaps="false" />


        <Button
            android:id="@+id/bt3"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="隐藏"
            android:textAllCaps="false" />


        <Button
            android:id="@+id/bt4"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="删除"
            android:textAllCaps="false" />


        <Button
            android:id="@+id/bt5"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="替换"
            android:textAllCaps="false" />

        <Button
            android:id="@+id/bt6"
            vb_click="@{v}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="是否存在"
            android:textAllCaps="false" />


        <FrameLayout
            android:layout_weight="1"
            android:id="@+id/llContent"
            android:layout_width="match_parent"
            android:layout_height="0dp" />
    </LinearLayout>

</layout>