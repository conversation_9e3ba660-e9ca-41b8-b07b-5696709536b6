<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".adapter.ItemDecorationDemoAdapter">

    <data>

        <import type="com.v.demo.R" />

        <variable
            name="bean"
            type="com.v.demo.bean.ItemDecorationDenoBean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@android:color/black">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@{bean.title}"
            android:textColor="@android:color/white"
            tools:text="title" />

    </LinearLayout>
</layout>