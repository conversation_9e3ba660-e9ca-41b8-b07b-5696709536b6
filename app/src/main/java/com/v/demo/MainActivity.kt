package com.v.demo

import android.view.View
import com.v.base.VBActivity
import com.v.base.utils.goActivity
import com.v.demo.databinding.MainActivityBinding
import com.v.demo.device.DeviceActivity
import com.v.demo.push.Push1Activity
import com.v.demo.scale.ScaleDemoActivity
//import com.v.demo.scale.ScaleDemoActivity
import com.v.demo.viewmodel.DemoViewModel


class MainActivity : VBActivity<MainActivityBinding, DemoViewModel>(), View.OnClickListener {
    override fun initData() {
        mDataBinding.v = this
    }

    override fun createObserver() {
    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.btVbDemo.id -> {
                goActivity(VbDemoActivity::class.java)
            }

            mDataBinding.btDevice.id -> {
                goActivity(DeviceActivity::class.java)

            }

            mDataBinding.btReport.id -> {
                goActivity(Push1Activity::class.java)
            }


            mDataBinding.btScaleDemo.id -> {
                goActivity(ScaleDemoActivity::class.java)
            }
        }
    }
}