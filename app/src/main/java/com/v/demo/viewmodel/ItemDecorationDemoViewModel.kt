package com.v.demo.viewmodel

import androidx.lifecycle.MutableLiveData
import com.v.demo.BaseViewModel
import com.v.demo.bean.ItemDecorationDenoBean

/**
 * author  : ww
 * desc    : 分割线演示
 * time    : 2024-03-26 17:05:35
 */
class ItemDecorationDemoViewModel : BaseViewModel() {

    var list = MutableLiveData<ArrayList<ItemDecorationDenoBean>>()

    fun getData(page: Int) {
        val ls = ArrayList<ItemDecorationDenoBean>()
        repeat(10)
        {
            ls.add(ItemDecorationDenoBean((page * it).toString()))
        }

        list.postValue(ls)
    }
}