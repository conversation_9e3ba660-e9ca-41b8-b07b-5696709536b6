package com.v.demo.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.mrk.device.bean.DeviceMyBindBean
import com.mrk.network.net.MrkNetwork
import com.mrk.network.net.executeResponse
import com.mrk.network.net.request
import com.mrk.network.net.requestDefault
import com.v.demo.BaseViewModel
import com.v.demo.bean.BannerBean
import com.v.demo.bean.HomeBean
import com.v.demo.net.NetworkApiResponse
import com.v.log.util.log
import kotlinx.coroutines.async
import kotlinx.coroutines.launch


/**
 * <AUTHOR> ww
 * desc    :
 * time    : 2021/1/12 16:11
 */
class DemoViewModel : BaseViewModel() {

    var homeBean = MutableLiveData<HomeBean>()

    var bannerBean = MutableLiveData<NetworkApiResponse<List<BannerBean>>>()


    //使用VLibrary库 网络请求
    fun getList(page: Int) {
        if (page == 1) {
            getBanner()
        }
        //使用VLibrary库 获取未处理的数据
        requestDefault({
            MrkNetwork.instance.get("https://www.wanandroid.com/article/list/${page}/json")
        }, homeBean)
    }

    //使用VLibrary库 网络请求
    private fun getBanner() {
        //使用VLibrary库 获取未处理的数据
        //bannerBean使用了NetworkApiResponse包装,而NetworkApiResponse又继承了基类BaseResponse,所以网络请求结果会根据NetworkApiResponse里面的isSuccess判断
        //requestDefault内部会根据传入的类型自行做判断
        requestDefault({
            MrkNetwork.instance.get("https://www.wanandroid.com/banner/json")
        }, bannerBean)

    }

    /**
     * 获取该用户所有设备
     * @param productType 产品分类 1.运动设备，3健康设备
     * @param productId 产品id
     */
    fun getDeviceMyBindBean(
        productType: String = "",
        productId: String = "",
    ) {
        val map = mutableMapOf<String, Any>()
        map["productType"] = productType
        map["productId"] = productId

        request<DeviceMyBindBean>({
            MrkNetwork.instance.post("/app/device-user-rel/pageMyDevices", map)
        }, success = {
            "ViewModel请求成功".log()
        }, error = {
            "ViewModel请求失败".log()
        }, dialog = true)
    }

    /**
     * 串行请求
     */
    fun performSequentialNetworkRequests() = viewModelScope.launch {

        val result1 = async {
            MrkNetwork.instance.post(
                "/app/device-user-rel/pageMyDevices",
                mapOf("productType" to "", "productId" to "")
            )
        }.await().executeResponse(DeviceMyBindBean::class.java)


        val result2 =
            async {
                MrkNetwork.instance.post(
                    "/app/device-user-rel/pageMyDevices",
                    mapOf("productType" to "", "productId" to (result1?.total ?: ""))
                )
            }.await().executeResponse(DeviceMyBindBean::class.java)


        val result3 =
            async {
                MrkNetwork.instance.post(
                    "/app/device-user-rel/pageMyDevices",
                    mapOf("productType" to "", "productId" to (result2?.size ?: ""))
                )
            }.await().executeResponse(DeviceMyBindBean::class.java)



        if (result1 != null && result2 != null && result3 != null) {
            "ViewModel串行请求成功".log()
        } else {
            "ViewModel串行请求失败".log()
        }

    }


}