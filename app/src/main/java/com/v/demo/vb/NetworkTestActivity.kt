package com.v.demo.vb

import android.view.View
import androidx.lifecycle.lifecycleScope
import com.mrk.device.bean.DeviceMyBindBean
import com.mrk.network.net.MrkNetwork
import com.mrk.network.net.executeResponse
import com.mrk.network.net.request
import com.v.base.VBActivity
import com.v.demo.R
import com.v.demo.databinding.ActivityNetworkTestBinding
import com.v.demo.viewmodel.DemoViewModel
import com.v.log.util.log
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 * author  : ww
 * desc    : 网络请求
 * time    : 2024-03-19 15:17:37
 */
class NetworkTestActivity : VBActivity<ActivityNetworkTestBinding, DemoViewModel>(),
    View.OnClickListener {

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.string_networktest_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this
    }

    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {

            mDataBinding.bt1.id -> {
                mViewModel.getDeviceMyBindBean()
            }

            mDataBinding.bt2.id -> {
                mViewModel.performSequentialNetworkRequests()
            }

            mDataBinding.bt3.id -> {
                getDeviceMyBindBean()
            }

            mDataBinding.bt4.id -> {
                performSequentialNetworkRequests()
            }

        }
    }


    private fun getDeviceMyBindBean() {
        val map = mutableMapOf<String, Any>()
        map["productType"] = ""
        map["productId"] = ""

        request<DeviceMyBindBean>({
            MrkNetwork.instance.post("/app/device-user-rel/pageMyDevices", map)
        }, success = {
            "非ViewModel请求成功".log()
        }, error = {
            "非ViewModel请求失败".log()
        })
    }


    /**
     * 串行请求
     */
    private fun performSequentialNetworkRequests() = this.lifecycleScope.launch {

        val result1 = async {
            MrkNetwork.instance.post(
                "/app/device-user-rel/pageMyDevices",
                mapOf("productType" to "", "productId" to "")
            )
        }.await().executeResponse(DeviceMyBindBean::class.java)


        val result2 =
            async {
                MrkNetwork.instance.post(
                    "/app/device-user-rel/pageMyDevices",
                    mapOf("productType" to "", "productId" to "asdfsdfsdf")
                )
            }.await().executeResponse(DeviceMyBindBean::class.java)


        val result3 =
            async {
                MrkNetwork.instance.post(
                    "/app/device-user-rel/pageMyDevices",
                    mapOf("productType" to "", "productId" to (result2?.size ?: ""))
                )
            }.await().executeResponse(DeviceMyBindBean::class.java)



        if (result1 != null && result2 != null && result3 != null) {
            "非ViewModel串行请求成功".log()
        } else {
            "非ViewModel串行请求失败".log()
        }

    }
}