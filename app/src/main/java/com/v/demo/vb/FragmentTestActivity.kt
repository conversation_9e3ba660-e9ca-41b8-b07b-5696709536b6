package com.v.demo.vb

import android.view.View
import com.v.base.VBActivity
import com.v.base.VBBlankViewModel
import com.v.base.utils.vbAddFragment
import com.v.base.utils.vbFindFragment
import com.v.base.utils.vbGetFragment
import com.v.base.utils.vbHideFragment
import com.v.base.utils.vbRemoveFragment
import com.v.base.utils.vbReplaceFragment
import com.v.base.utils.vbShowFragment
import com.v.base.utils.vbToast
import com.v.demo.R
import com.v.demo.databinding.DActivityFragmentTestBinding

/**
 * author  : ww
 * desc    : 测试Fragment
 * time    : 2023-12-14 15:26:40
 */
class FragmentTestActivity : VBActivity<DActivityFragmentTestBinding, VBBlankViewModel>(),
    View.OnClickListener {


    private val fragment by lazy{
        vbGetFragment("TwoFragment",TwoFragment::class.java)
    }

 private val fragment2 by lazy{
        vbGetFragment("OneFragment",OneFragment::class.java)
    }

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.d_string_fragmenttest_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this
    }

    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.bt1.id -> {
                vbAddFragment(fragment,mDataBinding.llContent.id)
            }

            mDataBinding.bt2.id -> {
                vbShowFragment(fragment)
            }

            mDataBinding.bt3.id -> {
                vbHideFragment(fragment)
            }

            mDataBinding.bt4.id -> {
                vbRemoveFragment(fragment)
            }

            mDataBinding.bt5.id -> {
                vbReplaceFragment(fragment2,mDataBinding.llContent.id)
            }

            mDataBinding.bt6.id -> {
                vbFindFragment("TwoFragment").vbToast()
            }
        }
    }

}