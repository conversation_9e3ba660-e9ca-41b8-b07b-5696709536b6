package com.v.demo.vb

import android.content.Intent
import com.v.demo.R
import com.v.base.VBActivity
import android.view.View
import com.hjq.language.MultiLanguages
import com.v.base.VBApplication
import com.v.base.VBBlankViewModel
import com.v.demo.MainActivity
import com.v.demo.databinding.DActivityLanguageTestBinding
import java.util.Locale

/**
 * author  : ww
 * desc    : 多语言测试
 * time    : 2023-12-14 15:29:42
 */
class LanguageTestActivity : VBActivity<DActivityLanguageTestBinding, VBBlankViewModel>(),
    View.OnClickListener {

    // 是否需要重启
    private var restart = false

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.d_string_languagetest_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this

        mDataBinding.tvLanguageActivity.text =
            this.resources.getString(R.string.string_current_language)

        mDataBinding.tvLanguageApplication.text =
            VBApplication.getApplication().resources.getString(R.string.string_current_language)

        mDataBinding.tvLanguageSystem.text = MultiLanguages.getLanguageString(
            mContext,
            MultiLanguages.getSystemLanguage(),
            R.string.string_current_language
        )
    }

    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.bt6.id -> {
                // 中文
                restart = MultiLanguages.setAppLanguage(mContext, Locale.CHINA)
                languages()
            }
            mDataBinding.bt7.id -> {
                // 英语
                restart = MultiLanguages.setAppLanguage(mContext, Locale.ENGLISH)
                languages()
            }
            mDataBinding.bt8.id -> {
                // 日文
                restart = MultiLanguages.setAppLanguage(mContext, Locale.JAPAN)
                languages()
            }
            mDataBinding.bt9.id -> {
                // 法语
                restart = MultiLanguages.setAppLanguage(mContext, Locale.FRENCH)
                languages()
            }

            mDataBinding.bt10.id -> {
                // 意大利语
                restart = MultiLanguages.setAppLanguage(mContext, Locale.ITALIAN)
                languages()
            }

            mDataBinding.bt11.id -> {
                // 德语
                restart = MultiLanguages.setAppLanguage(mContext, Locale.GERMAN)
                languages()
            }
            mDataBinding.bt12.id -> {
                // 西班牙语
                val locale =  Locale("es")
                restart = MultiLanguages.setAppLanguage(mContext, locale)
                languages()
            }
            mDataBinding.bt13.id -> {
                //跟随系统
                restart = MultiLanguages.clearAppLanguage(mContext)
                languages()
            }
            mDataBinding.bt14.id -> {
                //韩语(没有配置这个语言,如果没有 则会拿去values里面配置的语言)
                restart = MultiLanguages.setAppLanguage(mContext, Locale.KOREAN)
                languages()
            }
        }
    }

    private fun languages() {
        // 1.使用recreate来重启Activity的效果差，有闪屏的缺陷
        // recreate();

        // 2.使用常规startActivity来重启Activity，有从左向右的切换动画，稍微比recreate的效果好一点，但是这种并不是最佳的效果
        // startActivity(new Intent(this, LanguageActivity.class));
        // finish();

        // 3.我们可以充分运用 Activity 跳转动画，在跳转的时候设置一个渐变的效果，相比前面的两种带来的体验是最佳的
        if (restart) {
            startActivity(Intent(mContext, MainActivity::class.java))
            mContext.finish()
        }
    }

}