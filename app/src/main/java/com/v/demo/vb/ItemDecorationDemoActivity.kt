package com.v.demo.vb

import android.graphics.Color
import android.util.TypedValue
import android.view.View
import androidx.databinding.adapters.ViewBindingAdapter.setPadding
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.fondesa.recyclerviewdivider.Divider
import com.fondesa.recyclerviewdivider.Grid
import com.fondesa.recyclerviewdivider.Side
import com.fondesa.recyclerviewdivider.offset.DividerOffsetProvider
import com.fondesa.recyclerviewdivider.staggeredDividerBuilder
import com.fondesa.recyclerviewdivider.visibility.VisibilityProvider
import com.v.base.VBActivity
import com.v.base.utils.vbConfig
import com.v.base.utils.vbDivider
import com.v.base.utils.vbGetDataBinding
import com.v.base.utils.vbGrid
import com.v.base.utils.vbGridStaggered
import com.v.base.utils.vbLinear
import com.v.base.utils.vbLinearHorizontal
import com.v.base.utils.vbLoad
import com.v.demo.R
import com.v.demo.adapter.ItemDecorationDemoAdapter
import com.v.demo.databinding.DActivityItemDecorationDemoBinding
import com.v.demo.databinding.DActivityItemDecorationDemoFooterBinding
import com.v.demo.databinding.DActivityItemDecorationDemoHeadBinding
import com.v.demo.viewmodel.ItemDecorationDemoViewModel
import com.v.log.util.log


/**
 * author  : ww
 * desc    : 分割线演示
 * time    : 2024-03-26 17:05:35
 */
class ItemDecorationDemoActivity :
    VBActivity<DActivityItemDecorationDemoBinding, ItemDecorationDemoViewModel>(),
    View.OnClickListener {


    private val mAdapterHead by lazy {
        mContext.vbGetDataBinding<DActivityItemDecorationDemoHeadBinding>(R.layout.d_activity_item_decoration_demo_head)

    }

    private val mAdapterFooter by lazy {
        mContext.vbGetDataBinding<DActivityItemDecorationDemoFooterBinding>(R.layout.d_activity_item_decoration_demo_footer)
    }

    private var mAdapter: ItemDecorationDemoAdapter? = null
    private var page = 1
    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.d_string_itemdecorationdeno_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this
    }

    override fun createObserver() {
        mViewModel.list.observe(this, Observer {
            it?.run {
                page = mAdapter?.vbLoad(it, page, mDataBinding.refreshLayout) ?: 1
            }

        })
    }

    override fun onClick(v: View) {
        while (mDataBinding.recyclerView.itemDecorationCount > 0) {
            mDataBinding.recyclerView.removeItemDecorationAt(0)
        }
        mAdapter = null
        when (v.id) {
            mDataBinding.tvVertical.id -> {
                initVertical()
            }

            mDataBinding.tvHorizontal.id -> {
                initHorizontal()
            }

            mDataBinding.tvGrid.id -> {
                initGrid()
            }

            mDataBinding.tvStaggeredGrid.id -> {
                initStaggeredGrid()
            }
        }

//        mAdapter?.removeFooterView(mAdapterFooter.root)
//        mAdapter?.removeHeaderView(mAdapterHead.root)
//
//        mAdapter?.addHeaderView(mAdapterHead.root)
//        mAdapter?.addFooterView(mAdapterFooter.root)
        page = 1
        mViewModel.getData(page)


    }

    private fun initVertical() {
        mAdapter = mDataBinding.recyclerView
            .vbLinear(ItemDecorationDemoAdapter())
            .vbDivider {
                setColor(Color.GREEN)
                setDivider(10)
                setMargin(10, 30)
                setPadding(100)
            }
            .apply {
                vbConfig(mDataBinding.refreshLayout,
                    onRefresh = {
                        page = 1
                        mViewModel.getData(page)
                    },
                    onLoadMore = {
                        mViewModel.getData(page)
                    },
                    onItemClick = { adapter, view, position ->

                    })
            } as ItemDecorationDemoAdapter


    }


    private fun initHorizontal() {
        mAdapter = mDataBinding.recyclerView
            .vbLinearHorizontal(ItemDecorationDemoAdapter())
            .vbDivider {
                setColor(Color.GREEN)
                setDivider(10)
                setPadding(40)
            }
            .apply {
                vbConfig(mDataBinding.refreshLayout,
                    onRefresh = {
                        page = 1
                        mViewModel.getData(page)
                    },
                    onItemClick = { adapter, view, position ->

                    })
            } as ItemDecorationDemoAdapter

    }


    private fun initGrid() {
        mAdapter = mDataBinding.recyclerView
            .vbGrid(ItemDecorationDemoAdapter(), 3)
            .vbDivider {
                isCludeVisible = true
                setColor(Color.GREEN)
                setDivider(10)
                showSideDividers()
            }
            .apply {
                vbConfig(mDataBinding.refreshLayout,
                    onRefresh = {
                        page = 1
                        mViewModel.getData(page)
                    },
                    onLoadMore = {
                        mViewModel.getData(page)
                    },
                    onItemClick = { adapter, view, position ->

                    })
            } as ItemDecorationDemoAdapter

    }


    private fun initStaggeredGrid() {
        mAdapter = mDataBinding.recyclerView
            .vbGridStaggered(ItemDecorationDemoAdapter(), 3, StaggeredGridLayoutManager.VERTICAL)
            .vbDivider {
                hideSideDividers()
                setDivider(10)
                setColor(Color.GREEN)
            }
            .apply {
                vbConfig(mDataBinding.refreshLayout,
                    onRefresh = {
                        page = 1
                        mViewModel.getData(page)
                    },
                    onLoadMore = {
                        mViewModel.getData(page)
                    },
                    onItemClick = { adapter, view, position ->

                    })
            } as ItemDecorationDemoAdapter


    }
}