package com.v.demo.scale

import android.Manifest
import android.R
import android.app.Activity
import android.app.AlertDialog
import android.os.Build
import android.view.View
import android.widget.EditText
import androidx.core.app.ActivityCompat
import com.mrk.common.CommonApplication
import com.mrk.scale.DeviceScaleConstants
import com.mrk.scale.DeviceScaleListener
import com.mrk.scale.MrkScaleManager
import com.mrk.scale.bean.DeviceScaleConnectEnum
import com.mrk.scale.bean.DeviceScaleDataBean
import com.mrk.scale.bean.DeviceScaleMeasureEnum
import com.mrk.scale.bean.DeviceScaleSearchBean
import com.mrk.scale.bean.DeviceScaleSearchEnum
import com.mrk.scale.bean.DeviceScaleTypeEnum
import com.mrk.scale.bean.DeviceScaleUserBean
import com.v.base.VBActivity
import com.v.base.VBBlankViewModel
import com.v.base.utils.vbConfig
import com.v.base.utils.vbLinear
import com.v.demo.DemoApplication
import com.v.demo.databinding.ActivityScaleDemoBinding
import com.v.log.util.log
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale


class ScaleDemoActivity : VBActivity<ActivityScaleDemoBinding, VBBlankViewModel>(),
    View.OnClickListener {

    private var isWL = true

    private var height = 170
    private var age = 24
    private var sex = 1

    private val mAdapter by lazy {
        mDataBinding.recyclerView.vbLinear(ScaleDemoAdapter()).apply {
            vbConfig(
                mDataBinding.refreshLayout,
                onRefresh = {
                    data.clear()
                    startScanDevice()
                },
                onItemChildClick = { _, view, position ->
                    val bean = data[position] as DeviceScaleSearchBean
                    if (bean.connectEnum == DeviceScaleConnectEnum.ON) {
                        MrkScaleManager.disConnect()
                    } else {
                        MrkScaleManager.connect(bean)
                    }
                }
            )
        } as ScaleDemoAdapter
    }

    private val listener = object : DeviceScaleListener {

        override fun onSearchStatus(status: DeviceScaleSearchEnum) {
            when (status) {
                DeviceScaleSearchEnum.START -> {
                    mDataBinding.refreshLayout.autoRefreshAnimationOnly()
                    mDataBinding.tvStep.text = "搜索开始"
                }

                DeviceScaleSearchEnum.ING -> {
                    mDataBinding.tvStep.text = "搜索中"
                }

                DeviceScaleSearchEnum.STOP -> {
                    mDataBinding.refreshLayout.finishRefresh()
                    mDataBinding.tvStep.text = "搜索结束"
                }
            }
        }

        override fun onDeviceScaleMeasureStatus(status: DeviceScaleMeasureEnum) {

            when (status) {
                DeviceScaleMeasureEnum.MEASURE_WEIGHT_ING -> {
                    mDataBinding.tvStep.text = "体重测量中"
                }

                DeviceScaleMeasureEnum.MEASURE_BODY_FAT_ING -> {
                    mDataBinding.tvStep.text = "体脂测量中"
                }

                DeviceScaleMeasureEnum.MEASURE_HEART_RATE_ING -> {
                    mDataBinding.tvStep.text = "测量心率中"
                }

                DeviceScaleMeasureEnum.MEASURE_END -> {
                    mDataBinding.tvStep.text = "测量完成"
                }
            }
            mDataBinding.tvStep.text.toString().log()
        }

        override fun onConnectStatus(
            status: DeviceScaleConnectEnum,
            mac: String
        ) {

            when (status) {
                DeviceScaleConnectEnum.ON -> {
                    mViewModel.loadingChange.dismissDialog.postValue(true)
                    mDataBinding.tvStep.text = "连接成功"

                }

                DeviceScaleConnectEnum.OFF -> {
                    mViewModel.loadingChange.dismissDialog.postValue(true)
                    mDataBinding.tvStep.text = "未连接"

                }

                DeviceScaleConnectEnum.ING -> {
                    mViewModel.loadingChange.showDialog.postValue("正在连接....")
                    mDataBinding.tvStep.text = "正在连接"

                }
            }
            mAdapter.refresh()
        }

        override fun onSearchData(bean: DeviceScaleSearchBean) {
            "addData:${bean}".log()
            mAdapter.addData(bean)
        }

        override fun onDataProcess(weightStr: String) {
            mDataBinding.tvInfo.text =
                "过程数据 体重:$weightStr"
        }

        override fun onDataSuccess(weightStr: String, bean: DeviceScaleDataBean) {

            "收到了".log()
            mDataBinding.tvInfo.text =
                "测量成功 体重:$weightStr  \n$bean"

        }

    }

    override fun initData() {
        mDataBinding.v = this
        mAdapter
        requestBlePermission(mContext)
//        initWL()
    }

    override fun createObserver() {
    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.btSwitchScale.id -> {
                mDataBinding.tvInfo.text = ""
                mAdapter.data.clear()
                mAdapter.notifyDataSetChanged()
                MrkScaleManager.clear()
                mDataBinding.refreshLayout.finishRefresh()
                mDataBinding.tvBattery.text = ""

                if (isWL) {
                    initLF()
                } else {
                    initWL()
                }
                isWL = !isWL
            }

            mDataBinding.btStartScanDevice.id -> {
                startScanDevice()
            }

            mDataBinding.btStopScanDevice.id -> {
                stopScanDevice()
            }

            mDataBinding.btSetUser.id -> {
                setUserData()
            }

            mDataBinding.btResetScale.id -> {
                MrkScaleManager.resetScale()
            }

            mDataBinding.btConnect.id -> {
                if (isWL) {
                    MrkScaleManager.connect(
                        "AAA049",
                        "0C:43:1E:9D:31:01",
                        DeviceScaleTypeEnum.TYPE_4
                    )
                } else {
                    initWL()
                }
            }
        }
    }

    private fun initLF() {
        MrkScaleManager.create(CommonApplication.instance, DeviceScaleConstants.D_SERVICE_SCALE_LF)
        MrkScaleManager.addScaleListener(mContext.lifecycle, listener)
        mDataBinding.btSwitchScale.text = "体脂称:乐福"
        mDataBinding.btConnect.text = ""

    }

    private fun initWL() {
        MrkScaleManager.create(CommonApplication.instance, DeviceScaleConstants.D_SERVICE_SCALE_WL)
        MrkScaleManager.addScaleListener(mContext.lifecycle, listener)
        mDataBinding.btSwitchScale.text = "体脂称:沃莱"
        mDataBinding.btConnect.text = "直接连接:沃来 AAA049"
    }


    private fun startScanDevice() {
        mAdapter.data.clear()
        mDataBinding.refreshLayout.autoRefreshAnimationOnly()
        MrkScaleManager.startSearch()
    }

    private fun stopScanDevice() {
        mDataBinding.refreshLayout.finishRefresh()
        MrkScaleManager.stopSearch()
    }


    private fun setUserData() {
        val inputServer = EditText(this)
        inputServer.setText("170")
        val builder = AlertDialog.Builder(this)
        builder.setTitle("身高").setIcon(R.drawable.ic_dialog_info).setView(inputServer)
            .setNegativeButton("取消", null)
        builder.setPositiveButton(
            "确定"
        ) { dialog, which ->
            height = inputServer.text.toString().toInt()
            updateUserInfo()
        }
        builder.show()

        val inputServer2 = EditText(this)
        inputServer2.setText("24")
        val builder2 = AlertDialog.Builder(this)
        builder2.setTitle("年龄").setIcon(R.drawable.ic_dialog_info).setView(inputServer2)
            .setNegativeButton("取消", null)
        builder2.setPositiveButton(
            "确定"
        ) { dialog, which ->
            age = inputServer2.text.toString().toInt()
        }
        builder2.show()

        val inputServer3 = EditText(this)
        inputServer3.setText("1")
        val builder3 = AlertDialog.Builder(this)
        builder3.setTitle("性别(1:男,2:女)").setIcon(R.drawable.ic_dialog_info)
            .setView(inputServer3)
            .setNegativeButton("取消", null)
        builder3.setPositiveButton(
            "确定"
        ) { dialog, which ->
            sex = inputServer3.text.toString().toInt()

        }
        builder3.show()

    }

    private fun updateUserInfo() {
        MrkScaleManager.setScaleUserBean(
            DeviceScaleUserBean(
                height = height.toDouble(),
                birthday = getBirthDateForYearsAgo(age),
                sex = sex
            )
        )
        MrkScaleManager.getScaleUserBean().log()

    }

    private fun getBirthDateForYearsAgo(age: Int): String {
        val todayCalendar = Calendar.getInstance()
        todayCalendar.add(Calendar.YEAR, -age) // 减去 18 年

        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return sdf.format(todayCalendar.time)
    }

//    fun checkBlePermission(context: Context?): Boolean {
//        //Android target >=31(android 12)
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
//            return (ActivityCompat.checkSelfPermission(
//                context!!,
//                Manifest.permission.BLUETOOTH_CONNECT
//            ) == PackageManager.PERMISSION_GRANTED
//                    && ActivityCompat.checkSelfPermission(
//                context,
//                Manifest.permission.BLUETOOTH_SCAN
//            ) == PackageManager.PERMISSION_GRANTED)
//        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
//            return (ActivityCompat.checkSelfPermission(
//                context!!,
//                Manifest.permission.ACCESS_FINE_LOCATION
//            ) == PackageManager.PERMISSION_GRANTED
//                    && ActivityCompat.checkSelfPermission(
//                context,
//                Manifest.permission.ACCESS_COARSE_LOCATION
//            ) == PackageManager.PERMISSION_GRANTED)
//        }
//        return true
//    }


    fun requestBlePermission(activity: Activity?) {
        //Android target >=31(android 12)

        ActivityCompat.requestPermissions(
            activity!!, getPermissionsLocation(), 1
        )

    }


    /**
     * 连接所需要的权限
     */
    private fun getPermissionsLocation(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
            )
        } else {
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
            )
        }
    }
}