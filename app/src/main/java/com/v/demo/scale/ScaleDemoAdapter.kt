package com.v.demo.scale


import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.mrk.scale.bean.DeviceScaleConnectEnum
import com.mrk.scale.bean.DeviceScaleSearchBean
import com.v.demo.R
import com.v.demo.databinding.ActivityScaleDemoItemBinding


/**
 * author  : ww
 * desc    :
 * time    : 2024/3/21 13:39
 */
class ScaleDemoAdapter :
    BaseQuickAdapter<DeviceScaleSearchBean, BaseDataBindingHolder<ActivityScaleDemoItemBinding>>(
        R.layout.activity_scale_demo_item
    ) {

    init {
        addChildClickViewIds(R.id.tvConnect)
    }


    override fun convert(
        holder: BaseDataBindingHolder<ActivityScaleDemoItemBinding>,
        item: DeviceScaleSearchBean
    ) {
        holder.dataBinding?.run {
            tvName.text = item.deviceName
            tvBluetoothName.text = item.typeEnum.name
            tvMac.text = item.deviceMac

            when (item.connectEnum) {
                DeviceScaleConnectEnum.ON -> {
                    tvConnect.text = "已连接"
                }

                DeviceScaleConnectEnum.OFF -> {
                    tvConnect.text = "连接"
                }

                DeviceScaleConnectEnum.ING -> {
                    tvConnect.text = "连接中"
                }
            }
            executePendingBindings()
        }

    }

    fun refresh() {
        notifyDataSetChanged()
    }
}