package com.v.demo.device

import android.Manifest
import android.content.pm.PackageManager
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import com.cc.control.protocol.DeviceConstants
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.BluetoothEnum
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceOtaBean
import com.mrk.device.bean.DeviceSearchBean
import com.mrk.device.device.DeviceListener
import com.mrk.device.device.DevicePermissionUtils
import com.permissionx.guolindev.PermissionX
import com.v.base.VBActivity
import com.v.base.VBBlankViewModel
import com.v.base.dialog.VBListDialog
import com.v.base.utils.goActivity
import com.v.base.utils.vbConfig
import com.v.base.utils.vbLinear
import com.v.base.utils.vbToast
import com.v.demo.R
import com.v.demo.adapter.SearchDeviceAdapter
import com.v.demo.bean.TestListBean
import com.v.demo.databinding.ActivityDeviceBinding
import com.v.log.util.log
import com.v.log.util.logE

/**
 * author  : ww
 * desc    : 设备控制demo
 * time    : 2023-09-26 14:01:58
 */
class DeviceActivity : VBActivity<ActivityDeviceBinding, VBBlankViewModel>(), View.OnClickListener {

    private val mAdapter by lazy {
        mDataBinding.recyclerView.vbLinear(SearchDeviceAdapter()).apply {
            vbConfig(
                mDataBinding.refreshLayout,
                onRefresh = {
                    data.clear()
                    startSearch()
                },
                onItemClick = { adapter, view, position ->
                    val bean = data[position] as DeviceSearchBean
                    if (bean.connectEnum == DeviceConnectEnum.ON) {
                        mContext.goActivity(
                            DeviceDetailsActivity::class.java,
                            bundleOf(
                                DeviceDetailsActivity.BEAN to MrkDeviceManger.formatDeviceGoConnectBean(
                                    bean
                                )
                            )
                        )
                    }
                },
                onItemChildClick = { _, view, position ->
                    val bean = data[position] as DeviceSearchBean
                    deviceConnect(bean)
                }
            )
        } as SearchDeviceAdapter
    }


    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.string_device_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this
        mAdapter

        MrkDeviceManger
            .registerBluetoothStateListener {
                mDataBinding.tvBluetoothStatus.text = "蓝牙开关状态:${it}"
            }.registerDeviceListener(mContext, object : DeviceListener() {

                override fun onConnectStatus(isAutoReconnect: Boolean, bean: DeviceMangerBean) {
                    bean.connectEnum.name.log("全局的消息")
                    when (bean.connectEnum) {
                        DeviceConnectEnum.ON -> {
                            "连接成功".vbToast()
                            mViewModel.loadingChange.dismissDialog.postValue(true)
                        }

                        DeviceConnectEnum.OFF -> {
                            "断开连接".vbToast()
                            mViewModel.loadingChange.dismissDialog.postValue(true)
                        }

                        DeviceConnectEnum.ING -> {
                            mViewModel.loadingChange.showDialog.postValue("正在连接....")
                        }

                        DeviceConnectEnum.ERROR -> {
                            "连接失败".vbToast()
                            mViewModel.loadingChange.dismissDialog.postValue(true)

                        }
                    }
                    mAdapter.refresh(bean)
                }


            })
    }

    /**
     * 开始搜索
     */
    private fun startSearch() {
        MrkDeviceManger.startSearch(mContext, showBindDevice = 1, onSearchStatus = {
            if (it == BluetoothEnum.STOP) {
                mDataBinding.refreshLayout.finishRefresh()
            }
        }, onSearchDevice = {
            it.connectEnum = MrkDeviceManger.getDeviceStatus(it.mac)
            mAdapter.addData(it)
        })

    }

    /**
     * 设备连接
     */
    private fun deviceConnect(bean: DeviceSearchBean) {
        MrkDeviceManger
            .create(
                mContext,
                bean
            ).connect(onForwardToSettings = { scope, deniedList ->
                val message = "连接设备必须开启以下权限"
                scope.showForwardToSettingsDialog(
                    deniedList,
                    message,
                    "去开启", "取消"
                )
            })

    }


    override fun createObserver() {
    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.tvSearchStart.id -> {
                mDataBinding.refreshLayout.autoRefresh()
            }

            mDataBinding.tvSearchStop.id -> {
                MrkDeviceManger.stopSearch()
            }

            mDataBinding.tvMyDevice.id -> {
                goActivity(DeviceMyBindActivity::class.java)
            }

            mDataBinding.tvDeviceDetails.id -> {
                val list = ArrayList<TestListBean>()
                list.add(TestListBean("动感单车", DeviceConstants.D_BICYCLE))
                list.add(TestListBean("跑步机", DeviceConstants.D_TREADMILL))
                list.add(TestListBean("划船机", DeviceConstants.D_ROW))
                list.add(TestListBean("椭圆机", DeviceConstants.D_TECHNOGYM))

                VBListDialog(mContext)
                    .setTitle("List")
                    .setList(list)
                    .setClickListener { dialog, item, position ->
                        dialog.dismiss()
                        val bean = item as TestListBean
                        mContext.goActivity(
                            DeviceDetailsActivity::class.java,
                            bundleOf(
                                DeviceDetailsActivity.PRODUCT_ID to item.code
                            )
                        )
                    }.show()
            }
        }
    }


}