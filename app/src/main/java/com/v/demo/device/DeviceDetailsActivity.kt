package com.v.demo.device

import android.view.View
import com.cc.control.bean.DeviceTrainBO
import com.cc.control.protocol.DeviceConstants
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceGoConnectBean
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceOtaBean
import com.mrk.device.bean.DeviceTreadmillEnum
import com.mrk.device.device.DeviceControl
import com.mrk.device.device.DeviceListener
import com.mrk.device.listener.DeviceOtaListener
import com.v.base.VBActivity
import com.v.base.VBBlankViewModel
import com.v.base.dialog.VBHintDialog
import com.v.base.utils.goActivity
import com.v.base.utils.vbToJson
import com.v.base.utils.vbToast
import com.v.demo.R
import com.v.demo.databinding.ActivityDeviceDetailsBinding

/**
 * author  : ww
 * desc    : 设备详情
 * time    : 2024-01-12 13:50:31
 */
class DeviceDetailsActivity : VBActivity<ActivityDeviceDetailsBinding, VBBlankViewModel>(),
    View.OnClickListener {

    companion object {
        //连接类
        const val BEAN = "bean"

        //设备大类
        const val PRODUCT_ID = "productId"
    }

    private val deviceGoConnectBean by lazy {
        intent.extras!!.getParcelable<DeviceGoConnectBean>(BEAN)
    }

    private val productId by lazy {
        intent.extras!!.getString(PRODUCT_ID)!!
    }

    private var deviceControl: DeviceControl? = null

    private val deviceListener = object : DeviceListener() {

        override fun onDeviceOta(bean: DeviceOtaBean) {
            if (bean.downloadLink.isNullOrEmpty()) {
                return
            }
            val otaDialog = VBHintDialog(mContext)
                .setTitle("固件升级")
                .setContent(bean.updateLog)
                .setClickListener { hintDialog, position ->
                    hintDialog.dismiss()
                    if (position == 1) {
                        otaUpdate()
                    }
                }
            //更新类型1:选择更新，2:强制更新
            if (bean.updateType == 2) {
                otaDialog.setButtonText("立即升级")
            } else {
                otaDialog.setButtonText("取消", "升级")
            }
            otaDialog.show()
        }

        override fun onConnectStatus(isAutoReconnect: Boolean, bean: DeviceMangerBean) {
            mDataBinding.tvConnectStatus.text = "连接状态:${bean.connectEnum}"
            when (bean.connectEnum) {
                DeviceConnectEnum.ON -> {
                    mViewModel.loadingChange.dismissDialog.postValue(true)
                }

                DeviceConnectEnum.OFF -> {
                    mViewModel.loadingChange.dismissDialog.postValue(true)
                }

                DeviceConnectEnum.ING -> {
                    mViewModel.loadingChange.showDialog.postValue("正在连接....")
                }

                DeviceConnectEnum.ERROR -> {
                    mViewModel.loadingChange.dismissDialog.postValue(true)

                }
            }
        }

        override fun onDeviceTreadmillStatus(status: DeviceTreadmillEnum) {
            mDataBinding.tvDeviceStatus.text = "跑步机状态:${status}"

            when (status) {
                DeviceTreadmillEnum.COUNT_TIME -> {
                    mViewModel.loadingChange.showDialog.postValue("跑步机启动中")
                }

                DeviceTreadmillEnum.SLOW_DOWN -> {
                    mViewModel.loadingChange.showDialog.postValue("跑步机减速中")
                }

                DeviceTreadmillEnum.START -> {
                    mViewModel.loadingChange.dismissDialog.postValue(true)
                    deviceControl?.deviceStart()
                }

                else -> {
                    mViewModel.loadingChange.dismissDialog.postValue(true)

                }
            }
        }

        override fun onNotifyData(bean: DeviceTrainBO) {
            mDataBinding.tvContent.text = bean.vbToJson()
            mDataBinding.tvResistance.text = bean.drag.toString()
            mDataBinding.tvSpeed.text = bean.speed.toString()
            mDataBinding.tvSlope.text = bean.gradient.toString()
        }
    }

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.string_devicedetails_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this
    }

    private fun initDevice() {
        if (deviceControl == null) {
            if (deviceGoConnectBean != null) {
                mDataBinding.tvConnectStatus.text =
                    "连接状态:" + MrkDeviceManger.getDeviceStatus(deviceGoConnectBean!!.productId)
                        .toString()
                mDataBinding.tvConnectInfo.text = deviceGoConnectBean.toString()

                deviceControl = MrkDeviceManger.create(mContext, deviceGoConnectBean!!)
                    .setOnDeviceListener(deviceListener)
                    .registerDevice()
            } else {
                mDataBinding.tvConnectStatus.text =
                    "连接状态:" + MrkDeviceManger.getDeviceStatus(productId).toString()
                MrkDeviceManger.getDeviceGoConnectBean(productId)?.run {
                    mDataBinding.tvConnectInfo.text = this.toString()
                }
                deviceControl =
                    MrkDeviceManger.create(mContext, productId)
                        .setOnDeviceListener(deviceListener)
                        .registerDevice()
            }
            deviceControl?.autoConnect()
        }

    }

    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.btConnect.id -> {
                connect()
            }

            mDataBinding.btDisConnect.id -> {
                deviceControl?.disConnect()
            }

            mDataBinding.btUnBind.id -> {
                deviceControl?.unBindDevice {
                    finish()
                }
            }

            mDataBinding.btStart.id -> {
                deviceControl?.deviceStart()
            }

            mDataBinding.btPause.id -> {
                deviceControl?.devicePause()
            }

            mDataBinding.btDataClear.id -> {
                deviceControl?.clearData()
            }

            mDataBinding.btRegisterNotify.id -> {
                deviceControl?.setNotifyData(true)
            }

            mDataBinding.btUnRegisterNotify.id -> {
                deviceControl?.setNotifyData(false)
            }

            mDataBinding.btOta.id -> {
                otaUpdate()
            }

            mDataBinding.btResistance.id -> {
                deviceControl?.sendCommand(
                    resistance = mDataBinding.etResistance.text.toString().toInt()
                )
            }

            mDataBinding.btSpeed.id -> {
                deviceControl?.sendCommand(
                    speed = mDataBinding.etSpeed.text.toString().toFloat(),
                    slope = mDataBinding.etSlope.text.toString().toInt(),
                )
            }

            mDataBinding.btSpeedReduce.id -> {
                val speed = mDataBinding.tvSpeed.text.toString().toFloat()
                val speedDecimal = mDataBinding.etSpeedDecimal.text.toString().toFloat()
                val result = speed - speedDecimal
                deviceControl?.sendCommand(
                    speed = result,
                    slope = 0
                )

            }

            mDataBinding.btSpeedAdd.id -> {
                val speed = mDataBinding.tvSpeed.text.toString().toFloat()
                val speedDecimal = mDataBinding.etSpeedDecimal.text.toString().toFloat()
                val result = speed + speedDecimal
                deviceControl?.sendCommand(
                    speed = result,
                    slope = 0
                )
            }

            mDataBinding.btSlope.id -> {
                if (deviceControl?.getDeviceMangerBean()?.connectBean?.productId == DeviceConstants.D_TREADMILL) {
                    deviceControl?.sendCommand(
                        speed = mDataBinding.etSpeed.text.toString().toFloat(),
                        slope = mDataBinding.etSlope.text.toString().toInt()
                    )
                } else {
                    deviceControl?.sendCommand(
                        resistance = mDataBinding.etResistance.text.toString().toInt(),
                        slope = mDataBinding.etSlope.text.toString().toInt()
                    )
                }

            }
        }
    }

    private fun connect() {
        if (deviceControl == null) {
            mContext.goActivity(DeviceActivity::class.java)
            return
        }
        deviceControl?.connect(onAutoConnect = {
            VBHintDialog(mContext)
                .setContent("已有相同类型设备连接,是否断开已连接设备再连接?")
                .setButtonText("取消", "确定")
                .setClickListener { hintDialog, position ->
                    hintDialog.dismiss()
                    if (position == 1) {
                        MrkDeviceManger.disConnect(it.address) {
                            mDataBinding.btConnect.postDelayed({
                                connect()
                            }, 300)
                        }
                    }
                }.show()
        })

        deviceControl?.autoConnect()
    }

    private fun otaUpdate() {

        deviceControl?.otaUpdate(object : DeviceOtaListener {
            override fun onTheLatestVersion() {
                "当前已是最新版本".vbToast()
                mDataBinding.tvOtaContent.text = "当前已是最新版本"
            }

            override fun onStart() {
                //开始
                "ota开始".vbToast()
            }

            override fun onDownloadOtaProgress(
                readableOffset: String,
                readableTotalLength: String,
                speed: String,
                progress: Int
            ) {
                val progressStatus = "$readableOffset/$readableTotalLength"
                //ota下载进度
                mDataBinding.tvOtaContent.text =
                    "[$progressStatus]，下载速度:$speed，进度:$progress%"
                mDataBinding.otaProgressBar.progress = progress
            }

            override fun onInstallOtaProgress(progress: Int) {
                mDataBinding.tvOtaContent.text = "安装进度:$progress%"
                //安装进度
                mDataBinding.otaProgressBar.progress = progress
            }

            override fun onInstallOtaEnd() {
                "ota完成".vbToast()
            }

            override fun onError() {
                "ota失败".vbToast()
                mDataBinding.tvOtaContent.text = "ota失败"
            }

        })
    }

    /**
     * 在销毁的时候不放到onDestroy是因为 在华为部分手机上面 会有延迟回调onDestroy的问题
     */
    override fun onResume() {
        super.onResume()
        initDevice()
    }
}