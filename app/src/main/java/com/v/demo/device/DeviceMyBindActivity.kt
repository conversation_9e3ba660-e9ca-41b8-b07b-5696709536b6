package com.v.demo.device

import android.view.View
import androidx.core.os.bundleOf
import com.mrk.device.*
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceMyBindBean
import com.mrk.device.bean.DeviceOtaBean
import com.mrk.device.device.DeviceListener
import com.v.base.VBActivity
import com.v.base.VBBlankViewModel
import com.v.base.dialog.VBHintDialog
import com.v.base.utils.*
import com.v.demo.R
import com.v.demo.adapter.DeviceMyBindAdapter
import com.v.demo.databinding.DActivityDeviceMyBindBinding

/**
 * author  : ww
 * desc    : 我绑定的设备
 * time    : 2024-02-04 16:04:55
 */
class DeviceMyBindActivity : VBActivity<DActivityDeviceMyBindBinding, VBBlankViewModel>(),
    View.OnClickListener {

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            "我绑定的设备",
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }


    private val mAdapter by lazy {
        mDataBinding.recyclerView
            .vbLinear(DeviceMyBindAdapter())
            .vbDivider {
                setDivider(5)
            }
            .apply {
                vbConfig(mDataBinding.refreshLayout,
                    onRefresh = {
                        getData()
                    },
                    onItemClick = { adapter, view, position ->
                        val bean = data[position] as DeviceMyBindBean.Record
                        mContext.goActivity(
                            DeviceDetailsActivity::class.java,
                            bundleOf(
                                DeviceDetailsActivity.BEAN to MrkDeviceManger.formatDeviceGoConnectBean(
                                    bean
                                )
                            )
                        )
                    },
                    onItemChildClick = { adapter, view, position ->
                        val bean = data[position] as DeviceMyBindBean.Record
                        when (view.id) {
                            R.id.tvConnect -> {
                                if (MrkDeviceManger.isConnect(bean.mac)) {
                                    MrkDeviceManger.disConnect(bean.mac)
                                } else {
                                    deviceConnect(bean)
                                }
                            }

                            R.id.tvUnBindDevice -> {
                                VBHintDialog(mContext)
                                    .setContent("确定解绑吗?")
                                    .setButtonText("取消", "确定")
                                    .setClickListener { dialog, code ->
                                        dialog.dismiss()
                                        if (code == 1) {
                                            MrkDeviceManger.unBindDevice(
                                                mContext,
                                                bean.mac,
                                                bean.deviceUserRelId
                                            ) {
                                                data.remove(bean)
                                                notifyItemRemoved(position)
                                            }
                                        }
                                    }.show()

                            }
                        }


                    })
            } as DeviceMyBindAdapter
    }

    override fun initData() {
        mDataBinding.v = this

        MrkDeviceManger
            .registerDeviceListener(mContext, object : DeviceListener() {

                override fun onConnectStatus(isAutoReconnect: Boolean, bean: DeviceMangerBean) {
                    when (bean.connectEnum) {
                        DeviceConnectEnum.ON -> {
                            mViewModel.loadingChange.dismissDialog.postValue(true)
                        }

                        DeviceConnectEnum.OFF -> {
                            mViewModel.loadingChange.dismissDialog.postValue(true)
                        }

                        DeviceConnectEnum.ING -> {
                            mViewModel.loadingChange.showDialog.postValue("正在连接....")
                        }

                        DeviceConnectEnum.ERROR -> {
                            mViewModel.loadingChange.dismissDialog.postValue(true)

                        }
                    }
                    mAdapter.notifyDataSetChanged()
                }


            })

    }

    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {

        }
    }

    /**
     * 设备连接
     */
    private fun deviceConnect(bean: DeviceMyBindBean.Record) {
        MrkDeviceManger
            .create(
                mContext, bean
            ).connect(onForwardToSettings = { scope, deniedList ->
                val message = "连接设备必须开启以下权限"
                scope.showForwardToSettingsDialog(
                    deniedList,
                    message,
                    "去开启", "取消"
                )
            })

    }

    private fun getData() {
        mDataBinding.refreshLayout.autoRefreshAnimationOnly()
        MrkDeviceManger.getDeviceMyBindList(onSuccess = {
            mAdapter.vbLoad(it, refreshLayout = mDataBinding.refreshLayout)
        })
    }

    override fun onResume() {
        super.onResume()
        getData()
    }

}