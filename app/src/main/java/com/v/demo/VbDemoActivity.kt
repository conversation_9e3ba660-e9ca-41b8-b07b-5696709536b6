package com.v.demo

import com.v.base.VBActivity
import android.view.View
import androidx.fragment.app.Fragment
import com.v.base.VBBlankViewModel
import com.v.base.utils.vbGetFragment
import com.v.demo.databinding.ActivityVbDemoBinding
import com.v.demo.vb.OneFragment
import com.v.demo.vb.ThreeFragment
import com.v.demo.vb.TwoFragment
import com.v.demo.view.IndicatorZoom
import net.lucode.hackware.magicindicator.ViewPagerHelper

/**
 * author  : ww
 * desc    : VBDEMO
 * time    : 2023-09-26 13:56:16
 */
class VbDemoActivity : VBActivity<ActivityVbDemoBinding, VBBlankViewModel>(), View.OnClickListener {

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.string_vbdemo_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    private val commonNavigator by lazy {

        val titles = resources.getStringArray(R.array.dm_tab)
        val iconOffs = arrayOf(
            R.mipmap.ic_launcher,
            R.mipmap.ic_launcher,
            R.mipmap.ic_launcher
        )
        val iconOns = arrayOf(
            R.mipmap.ic_launcher,
            R.mipmap.ic_launcher,
            R.mipmap.ic_launcher
        )

        val fragments = ArrayList<Fragment>()
        fragments.add(vbGetFragment("home", OneFragment::class.java))
        fragments.add(vbGetFragment("home1", TwoFragment::class.java))
        fragments.add(vbGetFragment("home2", ThreeFragment::class.java))

        IndicatorZoom(
            this,
            mDataBinding.viewPager,
            fragments,
            titles,
            iconOffs,
            iconOns
        )

    }

    override fun initData() {
        mDataBinding.v = this
        initMg()
    }

    override fun createObserver() {
    }

    private fun initMg() {
        mDataBinding.magicIndicator.navigator = commonNavigator
        ViewPagerHelper.bind(mDataBinding.magicIndicator, mDataBinding.viewPager);
        mDataBinding.viewPager.currentItem = 0
    }

    override fun onClick(v: View) {
        when (v.id) {

        }
    }

}