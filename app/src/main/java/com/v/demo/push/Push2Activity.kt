package com.v.demo.push

import com.v.demo.R
import com.v.base.VBActivity
import android.view.View
import com.v.base.VBBlankViewModel
import com.v.base.utils.goActivity
import com.v.demo.databinding.ActivityPush2Binding

/**
 * author  : ww
 * desc    : Push2
 * time    : 2023-10-31 15:03:44
 */
class Push2Activity : VBActivity<ActivityPush2Binding, VBBlankViewModel>(), View.OnClickListener {

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.string_push2_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this
    }

    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.bt1.id -> {
                DataTagPushManager.instance.click("按钮11")
            }

            mDataBinding.bt2.id -> {
                DataTagPushManager.instance.request()
            }

            mDataBinding.bt3.id -> {
                goActivity(Push3Activity::class.java)
            }

            mDataBinding.bt4.id -> {
                PushDialog(mContext).show()
            }
        }
    }

}