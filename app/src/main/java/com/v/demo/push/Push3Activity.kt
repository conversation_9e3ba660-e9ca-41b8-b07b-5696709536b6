package com.v.demo.push

import com.v.demo.R
import com.v.base.VBActivity
import android.view.View
import com.v.base.VBBlankViewModel
import com.v.base.utils.goActivity
import com.v.demo.VbDemoActivity
import com.v.demo.databinding.ActivityPush3Binding

/**
 * author  : Push3
 * desc    : Push3
 * time    : 2023-10-31 15:04:19
 */
class Push3Activity : VBActivity<ActivityPush3Binding, VBBlankViewModel>(), View.OnClickListener {

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.string_push3_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this
    }

    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.bt1.id -> {
                DataTagPushManager.instance.click("按钮111")
            }

            mDataBinding.bt2.id -> {
                goActivity(VbDemoActivity::class.java)
            }

            mDataBinding.bt3.id -> {
                DataTagPushManager.instance.request()
            }
        }
    }

}