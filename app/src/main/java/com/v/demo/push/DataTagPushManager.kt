package com.v.demo.push


import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.v.base.utils.vbGetAppVersionName
import com.v.log.util.log
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar


/**
 * author  : ww
 * desc    : 数据埋点 管理类
 * time    : 2023/4/18 16:32
 */
class DataTagPushManager private constructor() {

    private var config: DataTagPushConfig = DataTagPushConfig()
    private var mContext: Application? = null

    private var addNumber = 0

    private var requestBlock: (suspend CoroutineScope.(data: JSONObject) -> Unit)? = null
    private var scope: CoroutineScope =
        CoroutineScope(CoroutineName("DataTagPush") + Dispatchers.IO)


    private val queue = JSONArray()

    //全局路径
    private val pathList = LinkedHashMap<String, DataTagPushBean>()

    private var claName = ""

    //当前页面的path也就是pageId 通过进入的Activity名称来获取
    private var pageId = ""
    private var exposureData: HashMap<String, String>? = null

    private var USER_ID: String = ""//用户id
    private val TERMINAL by lazy { 1 } //手机设备类型：terminal（1=android手机，2=android平板，3=ios手机，4=ios平板）
    private val UNIT_TYPE by lazy { Build.MANUFACTURER }//手机品牌型号
    private val DEVICE_VERSION by lazy { "${Build.VERSION.SDK_INT}" } //手机版本号
    private val APP_VERSION by lazy { mContext!!.vbGetAppVersionName() } //app版本号
    private val CHANNEL: String = ""//app下载渠道（1=app store，2=华为，3=小米，4=应用宝，5=OPPO，6=VIVO）


    private fun getLocalTime(): String {
        val calendar: Calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        return dateFormat.format(calendar.time)
    }

    //双重校验锁式
    companion object {
        val instance: DataTagPushManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            DataTagPushManager()
        }
    }


    fun init(
        mContext: Application,
        userId: String,
        block: suspend CoroutineScope.(data: JSONObject) -> Unit,
        config: DataTagPushConfig = DataTagPushConfig()
    ) {
        this.mContext = mContext
        this.USER_ID = userId
        this.config = config
        this.requestBlock = block
        timerUpload()
        //记录留存时间
        var createActTime: Long = 0
        var createFrgTime: Long = 0
        mContext.registerActivityLifecycleCallbacks(object :
            Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(p0: Activity, p1: Bundle?) {
                if (p0 is AppCompatActivity) {
                    p0.supportFragmentManager
                        .registerFragmentLifecycleCallbacks(object :
                            FragmentManager.FragmentLifecycleCallbacks() {

                            override fun onFragmentResumed(fm: FragmentManager, f: Fragment) {
                                super.onFragmentResumed(fm, f)

                                if (f.javaClass.name.isPass()) {
                                    createFrgTime = System.currentTimeMillis()
                                    claName = f.javaClass.simpleName
                                    pageId = config.pageIdList[claName] ?: claName
                                    pathList[claName] = DataTagPushBean(pageId)
                                }
                            }

                            override fun onFragmentPaused(fm: FragmentManager, f: Fragment) {
                                super.onFragmentPaused(fm, f)
                                if (f.javaClass.name.isPass()) {
                                    exposure(duration = (System.currentTimeMillis() - createFrgTime) / 1000)
                                }
                            }

                            override fun onFragmentDestroyed(fm: FragmentManager, f: Fragment) {
                                super.onFragmentDestroyed(fm, f)
                                pathClear(f.javaClass.simpleName)
                            }

                        }, false)


                }
            }

            override fun onActivityStarted(p0: Activity) {
            }

            override fun onActivityResumed(p0: Activity) {
                if (p0.javaClass.name.isPass()) {
                    createActTime = System.currentTimeMillis()
                    claName = p0.javaClass.simpleName
                    pageId = config.pageIdList[claName] ?: claName
                    pathList[claName] = DataTagPushBean(pageId)
                }
            }

            override fun onActivityPaused(p0: Activity) {
                if (p0.javaClass.name.isPass()) {
                    exposure(duration = (System.currentTimeMillis() - createActTime) / 1000)
                }
            }

            override fun onActivityStopped(p0: Activity) {
            }

            override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {
            }

            override fun onActivityDestroyed(p0: Activity) {
                pathClear(p0.javaClass.simpleName)
            }

        })
    }


    /**
     * 设置用户id
     */
    fun setUserId(userId: String) {
        this.USER_ID = userId
    }


    /**
     * 添加点击事件埋点
     * @param eventCode 当前点击的控件
     */
    fun click(eventCode: String) {
        click(key = pageId, eventCode = eventCode)
    }

    /**
     * 添加点击事件埋点
     * @param eventCode 当前点击的控件
     * @param data 额外的属性(有些点击事件需要新增传参)
     */
    fun click(eventCode: String, data: HashMap<String, String>) {
        click(key = pageId, eventCode = eventCode, data = data)
    }


    /**
     * 添加点击事件埋点
     * @param eventCode 当前点击的控件
     * @param s 额外的属性(有些点击事件需要新增传参)
     */
    fun click(eventCode: String, s: String = "", type: DataTagPushType? = null) {
        var data: HashMap<String, String>? = null
        if (type != null) {
            data = HashMap()
            when (type) {
                DataTagPushType.ACTIVITY -> {
                    //活动ID
                    data["activity_id"] = s
                }

                DataTagPushType.COURSE -> {
                    //课程ID
                    data["course_id"] = s
                }

                DataTagPushType.PLAN -> {
                    //计划ID
                    data["plan_id"] = s
                }

                DataTagPushType.TYPE -> {
                    //类目ID
                    data["type_id"] = s
                }

                DataTagPushType.THEME -> {
                    //主题ID
                    data["theme_id"] = s
                }
            }
        }
        click(key = pageId, eventCode = eventCode, data = data)
    }

    /**
     * 添加点击事件埋点
     * @param key 当前页面pageId(一般不用传,会通过当前打开的上下文自动取获取)
     * @param eventCode 当前点击的控件
     * @param data 额外的属性(有些点击事件需要新增传参)
     */
    fun click(key: String = pageId, eventCode: String, data: HashMap<String, String>? = null) {

        if (key != pageId) {
            setPath(key, eventCode)
        }

        val jsonObject = JSONObject()
        jsonObject["eventType"] = 2
        jsonObject["pageId"] = key
        jsonObject["eventCode"] = eventCode
        jsonObject["path"] = getPath()

        data?.run {
            jsonObject["attribute"] = this
        }
        queue.add(dataFormat(jsonObject))

        //如果key不等于当前直接获取的pageId 则表示是控件的id,在生成埋点数据以后,要把链路里面的记录去掉
        if (key != pageId) {
            pathClear(key)
        }
    }


    /**
     * 添加曝光事件埋点
     * @param data 曝光事件需要带入的参数
     */
    fun exposureData(data: HashMap<String, String>): DataTagPushManager {
        this.exposureData = data
        return this
    }


    /**
     * 添加曝光事件埋点
     * @param key 当前页面pageId(一般不用传,会通过当前打开的上下文自动取获取)
     * @param duration 当前页面停留时长
     */
    fun exposure(key: String = pageId, duration: Long) {
        //在曝光数据写入前,判断最后的数据是不是有eventCode有就写进去
        var eventCode = ""
        if (queue.size > 0) {
            val js = queue[queue.lastIndex] as JSONObject
            if (js.containsKey("eventCode")) {
                eventCode = js.getString("eventCode")
            }
        }
        pathList[if (key != pageId) key else claName]?.run {
            this.eventCode = eventCode
        }

        val jsonObject = JSONObject()
        jsonObject["eventType"] = 1
        jsonObject["pageId"] = key
        jsonObject["duration"] = duration
        jsonObject["path"] = getPath()
        exposureData?.run {
            jsonObject["attribute"] = this
        }
        queue.add(dataFormat(jsonObject))
        exposureData = null

        //如果key不等于当前直接获取的pageId 则表示是控件的id,在生成埋点数据以后,要把链路里面的记录去掉
        if (key != pageId) {
            pathClear(key)
        }
    }

    /**
     * 获取链路
     */
    fun getPath(): String {
        val sb = StringBuffer()
        pathList.keys.forEach {
            val bean = pathList[it]!!
            sb.append(bean.pageId)

            if (!bean.pageIdEdit.isNullOrEmpty()) {
                sb.append("/" + bean.pageIdEdit)
            }

            if (!bean.eventCode.isNullOrEmpty()) {
                sb.append(":${bean.eventCode}/")
            } else {
                sb.append("/")
            }

        }
        return sb.toString()
    }

    /**
     * h5设置路径
     */
    fun setPath(path: String, eventCode: String = "") {
        val bean = pathList[claName]
        //如果已经有了老的路径了就要在新的路径前面拼接上
        if (bean != null && !bean.pageIdEdit.isNullOrEmpty() && !bean.pageIdEdit.contains(path)) {
            pathList[claName] = DataTagPushBean(pageId, bean.pageIdEdit + "/" + path, eventCode)
        } else {
            pathList[claName] = DataTagPushBean(pageId, path, eventCode)
        }

    }

    /**
     * 清除路径
     */
    fun pathClear(key: String) {
        pathList.remove(key)
    }

    /**
     * 清除自定义路径
     */
    fun pathClearEdit(key: String) {
        pathList[claName]?.run {
            this.pageIdEdit = ""
        }
    }


    /**
     * 数据格式一下 设置公共属性
     */
    private fun dataFormat(jSONObject: JSONObject): JSONObject {
        //公共属性
        val js = JSONObject()
        js["userId"] = USER_ID
        js["terminal"] = TERMINAL
        js["unitType"] = UNIT_TYPE
        js["deviceVersion"] = DEVICE_VERSION
        js["appVersion"] = APP_VERSION
        js["eventLocalTime"] = getLocalTime()
        js.putAll(jSONObject)

//        js.log()
        addNumber++
        if (addNumber >= config.pushDataSize) {
            addNumber = 0
            request()
        }
        return js
    }


    /**
     * 请求接口
     */
    @Synchronized
    fun request() {
        if (getRequestData() == null) {
            return
        }
//        scope.launch {
//            runCatching {
//                requestBlock?.let { it(getRequestData()!!) }
//            }.onSuccess {
//                queue.clear()
//            }.onFailure {
//
//            }
//
//        }
//        getPath().log("路径")
    }

    private fun getRequestData(): JSONObject? {
        return if (queue.size > 0) {
            val jsonObject = JSONObject()
            jsonObject["reportTime"] = getLocalTime()
            jsonObject["events"] = queue
            jsonObject
        } else {
            null
        }
    }


    /**
     * 定时上传
     */
    private fun timerUpload() {
        scope.launch {
            while (true) {
                request()
                delay(config.pushDataTime)
            }
        }
    }

    fun clear() {
        setUserId("")
        queue.clear()
//        clickQueue.clear()
    }

    /**
     * 判断当前的className是否为需要监听的,避免有脏数据
     */
    fun String.isPass(): Boolean {
        return (this.startsWith(config.REGEX_PACKAGE_NAME)
                || this.startsWith(config.REGEX_MODULE_NAME))
    }

}

/**
 * 埋点点击
 */

fun String.tagClick() {
    DataTagPushManager.instance.click(eventCode = this)
}

fun String.tagClick(key: String) {
    DataTagPushManager.instance.click(key = key, eventCode = this)
}

fun String.tagClick(data: HashMap<String, String>) {
    DataTagPushManager.instance.click(eventCode = this, data = data)
}

fun String.tagClick(key: String, data: HashMap<String, String>) {
    DataTagPushManager.instance.click(key = key, eventCode = this, data = data)
}

fun String.tagClick(data: String, type: DataTagPushType) {
    DataTagPushManager.instance.click(eventCode = this, s = data, type)
}


/**
 * 曝光
 */
fun String.tagExposure(duration: Long, data: HashMap<String, String>? = null) {
    if (data != null) {
        DataTagPushManager.instance.exposureData(data)
    }
    DataTagPushManager.instance.exposure(this, duration)
}

/**
 * 曝光
 */
fun Context.tagExposureData(data: HashMap<String, String>) {
    DataTagPushManager.instance.exposureData(data)
}

/**
 * 手动设置自定义路径(切记要配套调用 tagPathClear)
 */
fun String.tagPath() {
    DataTagPushManager.instance.setPath(this)
}

/**
 * 手动清除自定义路径
 */
fun String.tagPathClear() {
    DataTagPushManager.instance.pathClearEdit(this)
}

