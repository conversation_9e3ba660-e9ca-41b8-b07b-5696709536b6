package com.v.demo.push

import android.content.Context
import android.view.View
import com.v.base.dialog.VBDialog
import com.v.base.utils.goActivity
import com.v.demo.databinding.DialogPushBinding

/**
 * author  : ww
 * desc    : PushDialog
 * time    : 2023-10-31 15:05:10
 */
class PushDialog(private val mContext: Context) : VBDialog<DialogPushBinding>(mContext),
    View.OnClickListener {

    private val tag ="弹窗路径path"

    private var listener: ((dialog: PushDialog, position: Int) -> Unit)? = null

    fun setClickListener(listener: ((dialog: PushDialog, position: Int) -> Unit)): PushDialog {
        this.listener = listener
        return this
    }

    override fun initData() {
        mDataBinding.v = this
        tag.tagPath()
    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.tvLeft.id -> {
                "tvLeft".tagClick(hashMapOf("bt" to "tvLeft"))
//                dismiss()
                listener?.invoke(this, 0)

            }

            mDataBinding.tvRight.id -> {
                "tvRight".tagClick(hashMapOf("bt" to "tvRight"))
                dismiss()
                listener?.invoke(this, 1)
                mContext.goActivity(Push3Activity::class.java)

            }
        }
    }

    override fun dismiss() {
        super.dismiss()
        DataTagPushManager.instance.exposure(tag, getResidenceTime())
        tag.tagPathClear()
    }

}