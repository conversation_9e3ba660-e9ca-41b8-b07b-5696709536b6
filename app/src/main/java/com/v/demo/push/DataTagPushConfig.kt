package com.v.demo.push


/**
 * author  : ww
 * desc    : 数据埋点 管理类
 * time    : 2023/4/18 16:32
 */
class DataTagPushConfig {

    //上限提交数量(当本地埋点时间达到10,提交数据)
    val pushDataSize = 30

    //上限提交时间(当本地埋点时间达到180000L3分钟的时候,提交数据)
    val pushDataTime = 180000L


    val REGEX_PACKAGE_NAME = "com.v.demo" //包名开头

    val REGEX_MODULE_NAME = "com.v.demo" //模块名开头


    //对应act的tag
    val pageIdList by lazy {
        hashMapOf(
            "Push1Activity" to "页面1",
            "Push2Activity" to "页面2",
            "Push3Activity" to "页面3",
            "HomeFragment" to "page_home",
            "DeviceActivity" to "device_home",
            "NetworkActivity" to "test_home"
        )
    }


}