package com.v.demo.push

import com.v.demo.R
import com.v.base.VBActivity
import android.view.View
import com.mrk.device.bean.DeviceMyBindBean
import com.mrk.network.net.MrkNetwork
import com.mrk.network.net.request
import com.v.base.utils.goActivity
import com.v.demo.databinding.ActivityPush1Binding
import com.v.demo.viewmodel.DemoViewModel

/**
 * author  : ww
 * desc    : Push1
 * time    : 2023-10-31 15:04:05
 */
class Push1Activity : VBActivity<ActivityPush1Binding, DemoViewModel>(), View.OnClickListener {

    override fun toolBarTitle(
        title: String,
        titleColor: Int,
        isShowBottomLine: Boolean,
        resLeft: Int,
        listenerLeft: View.OnClickListener?
    ): Boolean {
        super.toolBarTitle(
            this.getString(R.string.string_push1_title),
            titleColor,
            isShowBottomLine,
            resLeft,
            listenerLeft
        )
        return true
    }

    override fun initData() {
        mDataBinding.v = this
    }

    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.bt1.id -> {
                DataTagPushManager.instance.click("按钮1")
            }

            mDataBinding.bt2.id -> {
                DataTagPushManager.instance.click("按钮2")
                goActivity(Push2Activity::class.java)
            }

            mDataBinding.bt3.id -> {
                DataTagPushManager.instance.request()
            }

            mDataBinding.bt4.id -> {
                goActivity(Push2Activity::class.java)
            }
        }
    }

    fun getDeviceMyBindBean(
        productType: String = "",
        productId: String = "",
        onSuccess: (List<DeviceMyBindBean.Record>) -> Unit
    ) {
        val map = mutableMapOf<String, Any>()
        map["productType"] = productType
        map["productId"] = productId

        request<DeviceMyBindBean>({
            MrkNetwork.instance.post("/app/device-user-rel/pageMyDevices", map)
        }, success = {
            onSuccess.invoke(it.records)
        }, showErrorToast = true)
    }

}