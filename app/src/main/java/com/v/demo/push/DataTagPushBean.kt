package com.v.demo.push

/**
 * author  : ww
 * desc    :
 * time    : 2023/10/31 15:37
 */
/**
 * author  : ww
 * desc    :
 * time    : 2023/10/31 15:37
 */

/**
 * 路径对象
 * @param pageId 当前上下文的路径
 * @param pageId 重新写入的路径
 * @param eventCode 事件
 */
data class DataTagPushBean(
    var pageId: String,
    var pageIdEdit: String = "",
    var eventCode: String = ""
)


/**
 * 点击附带属性枚举
 * @param ACTIVITY 活动id
 * @param COURSE 课程id
 * @param PLAN 计划id
 * @param TYPE 类目id
 * @param THEME 主题id
 */
enum class DataTagPushType {
    ACTIVITY, COURSE, PLAN, TYPE, THEME
}