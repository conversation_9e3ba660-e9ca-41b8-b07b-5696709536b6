package com.v.demo

import android.animation.ObjectAnimator
import android.os.Bundle
import com.v.demo.R
import com.v.base.VBActivity
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.widget.RelativeLayout
import androidx.core.animation.doOnEnd
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.lifecycleScope
import com.v.base.VBBlankViewModel
import com.v.base.utils.goActivity
import com.v.demo.databinding.ActivitySplashBinding
import com.v.log.util.log
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * author  : ww
 * desc    : Splash
 * time    : 2024-05-21 11:16:46
 */
class SplashActivity : VBActivity<ActivitySplashBinding, VBBlankViewModel>(), View.OnClickListener {

    override fun useTranslucent(): Boolean {
        return true
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val splashScreen = installSplashScreen()
        super.onCreate(savedInstanceState)
//        splashScreen.setKeepOnScreenCondition { true }

    }

    override fun initData() {
        mDataBinding.v = this
        lifecycleScope.launch {
            delay(1000)
            mContext.goActivity(MainActivity::class.java)
            finish()
        }

    }


    override fun createObserver() {

    }

    override fun onClick(v: View) {
        when (v.id) {

        }
    }


}