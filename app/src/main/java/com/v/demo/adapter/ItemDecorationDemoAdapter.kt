package com.v.demo.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.v.demo.R
import com.v.demo.bean.ItemDecorationDenoBean
import com.v.demo.databinding.DActivityItemDecorationDemoItemBinding

/**
 * author  : ww
 * desc    : 分割线演示
 * time    : 2024-03-26 17:05:35
 */
class ItemDecorationDemoAdapter :
    BaseQuickAdapter<ItemDecorationDenoBean, BaseDataBindingHolder<DActivityItemDecorationDemoItemBinding>>(
        R.layout.d_activity_item_decoration_demo_item
    ) {

    override fun convert(
        holder: BaseDataBindingHolder<DActivityItemDecorationDemoItemBinding>,
        item: ItemDecorationDenoBean
    ) {
        holder.dataBinding?.run {
            bean = item
            executePendingBindings()
        }
    }
    fun sdfsdf()
    {

    }
}