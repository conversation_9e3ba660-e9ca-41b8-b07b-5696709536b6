package com.v.demo.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceMyBindBean

import com.v.demo.R
import com.v.demo.databinding.DActivityDeviceMyBindItemBinding

/**
 * author  : ww
 * desc    : 我绑定的设备
 * time    : 2024-02-04 16:04:55
 */
class DeviceMyBindAdapter :
    BaseQuickAdapter<DeviceMyBindBean.Record, BaseDataBindingHolder<DActivityDeviceMyBindItemBinding>>(
        R.layout.d_activity_device_my_bind_item
    ) {

    init {
        addChildClickViewIds(R.id.tvConnect, R.id.tvUnBindDevice)
    }


    override fun convert(
        holder: BaseDataBindingHolder<DActivityDeviceMyBindItemBinding>,
        item: DeviceMyBindBean.Record
    ) {
        holder.dataBinding?.run {

            //如果设备的mac地址为空  则通过设备的蓝牙名称去做匹配
            if (item.mac.isNullOrEmpty()) {
                MrkDeviceManger.getDeviceMangerBean(item.bluetoothName)?.run {
                    item.mac = this.connectBean.mac

                }
            }

            bean = item
            when (MrkDeviceManger.getDeviceMangerBean(item.mac)?.connectEnum) {

                DeviceConnectEnum.ON -> {
                    tvConnect.text = "已连接"
                }

                DeviceConnectEnum.ING -> {
                    tvConnect.text = "连接中"
                }

                else -> {
                    tvConnect.text = "连接"
                }
            }
            executePendingBindings()
        }
    }

}