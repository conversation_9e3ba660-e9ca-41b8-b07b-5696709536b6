package com.v.demo.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceSearchBean
import com.v.demo.R
import com.v.demo.databinding.ActivityDeviceDetailsItemBinding

/**
 * author  : ww
 * desc    :
 * time    : 2024/1/12 15:12
 */
class SearchDeviceAdapter :
    BaseQuickAdapter<DeviceSearchBean, BaseDataBindingHolder<ActivityDeviceDetailsItemBinding>>(
        R.layout.activity_device_details_item
    ) {

    init {
        addChildClickViewIds(R.id.tvConnect)
    }

    override fun convert(
        holder: BaseDataBindingHolder<ActivityDeviceDetailsItemBinding>,
        item: DeviceSearchBean
    ) {
        holder.dataBinding?.run {
            bean = item
            when (item.connectEnum) {

                DeviceConnectEnum.ON -> {
                    tvConnect.text = "已连接"
                }

                DeviceConnectEnum.ING -> {
                    tvConnect.text = "连接中"
                }

                else -> {
                    tvConnect.text = "连接"
                }
            }
            executePendingBindings()
        }
    }

    fun refresh(bean: DeviceMangerBean) {
        for (i in 0 until data.size) {
            if (data[i].mac == bean.connectBean.mac) {
                data[i].connectEnum = bean.connectEnum
                notifyDataSetChanged()
                break
            }
        }
    }
}