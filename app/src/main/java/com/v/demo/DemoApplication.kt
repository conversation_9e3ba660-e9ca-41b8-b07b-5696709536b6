package com.v.demo

import com.mrk.common.CommonApplication
import com.mrk.network.MrkNetworkConfig
import com.mrk.network.net.MrkNetOptions
import com.mrk.network.net.MrkNetwork
import com.mrk.scale.MrkScaleManager
import com.v.base.VBConfig
import com.v.base.VBConfigOptions
import com.v.demo.net.NetworkExceptionHandling
import com.v.demo.net.NetworkHeadInterceptor
import com.v.demo.push.DataTagPushManager
import com.v.log.LogConfig

class DemoApplication : CommonApplication() {

    override fun logConfig(): LogConfig {
        return LogConfig(this, true, true)
    }

    override fun initData() {
        super.initData()
        //基类配置
        val vbOptions = VBConfigOptions.Builder()
            .setIconFontPath("iconfont.ttf")
            .build()
        VBConfig.init(vbOptions)

        //网络请求配置
        val netOptions = MrkNetOptions.Builder()
            .setBaseUrl("https://7000.mrkw.cn:2443/")
            .setInterceptor(NetworkHeadInterceptor())
            .setExceptionHandling(NetworkExceptionHandling())
            .build()

        MrkNetworkConfig.init(netOptions)
        MrkScaleManager.init(this, true)

        DataTagPushManager.instance.init(this, "instance.userInfoBean.userBasicDataDTO.accountId", {
            MrkNetwork.instance.post("/app/behavior_event/report", it)
        })

    }

}