package com.v.demo.net

import android.os.Build
import com.mrk.common.CommonApplication
import com.v.base.utils.vbGetAppVersionCode
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * 自定义头部参数拦截器，传入heads
 */
class NetworkHeadInterceptor : Interceptor {

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val builder = chain.request().newBuilder()
        builder.addHeader("Content-Type", "application/json")
        builder.addHeader(
            "Authorization",
            "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjE3OTA1ODQ1NDEzNjM0MzM0NzMiLCJ0eXBlIjoxLCJ1dWlkIjoiZjE2NGQwOWE5ODk5NDExNmI0NTg3OWE0NTc2NmRiNjEiLCJuaWNrbmFtZSI6IlYiLCJhdmF0YXIiOiJodHRwczovL21lcml0LWFwcC10ZXN0Lm9zcy1jbi1oYW5nemhvdS5hbGl5dW5jcy5jb20vdXNlci1hdmF0YXIvMjAyNC0xMC0yOC8wOWFhNzhkMjRiY2I0MzEzODk5YjIwOGE1NjVjYzcyYl8xMDgweDE5MjAuanBnIiwibG9naW4iOjEsImV4cCI6MTc2MTE5Njc2OH0.hVYMb_eoN8z3aeyXedSQTxfXvIEv8wLC5toRX9q0ZkTe5vjlyhBAEeiAIPsYMDliWTMd12ZPtAi1Gm8i9uLX8Q"
        )
        //手机厂商名、产品名、手机型号、设备名
        val deviceName =
            Build.MANUFACTURER + " " + Build.PRODUCT + " " + Build.MODEL + " " + Build.DEVICE
        builder.addHeader(
            "User-Agent",
            CommonApplication.instance.vbGetAppVersionCode()
                .toString() + "," + deviceName + "," + Build.VERSION.SDK_INT
        )
        return chain.proceed(builder.build())
    }

}