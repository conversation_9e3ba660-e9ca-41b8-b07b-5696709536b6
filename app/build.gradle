plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
}

android {
    compileSdk AndroidVersions.compileSdkVersion
    defaultConfig {
        applicationId "com.v.demo"
        minSdk AndroidVersions.minSdkVersion
        targetSdk AndroidVersions.targetSdkVersion
        versionCode 100
        versionName "1.0.0"
        ndk { abiFilters "armeabi-v7a" }

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildFeatures {
        dataBinding = true
    }

    signingConfigs {
        config {
            storeFile file("../android.keystore")
            storePassword "ttmb123"
            keyAlias "ttmb"
            keyPassword "ttmb123"
        }
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    packagingOptions {
        exclude 'resources.arsc'
        exclude 'AndroidManifest.xml'
    }
}

dependencies {
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
    }
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation "com.github.hackware1993:MagicIndicator:1.7.0"
    implementation 'androidx.core:core-splashscreen:1.0.1'
//    implementation 'com.mrk.scale:mrkScale:${MavenRun.mavenVersion}'
//    if (MavenRun.isMaven) {
//        implementation("com.mrk.common:mrkCommon:${MavenRun.mavenVersion}") { changing = true }
//    } else {
    implementation project(':mrkCommon')
    implementation project(':mrkScale')
//    }
}