package com.mrk.device.device

import android.app.Application
import android.content.Context
import android.widget.Toast
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.cc.control.BluetoothManager
import com.mrk.device.DeviceViewModel
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.BluetoothEnum
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceGoConnectBean
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceMyBindBean
import com.mrk.device.bean.DeviceSearchBean
import com.permissionx.guolindev.request.ForwardScope
import com.v.log.util.log


/**
 * author  : ww
 * desc    :
 * time    : 2023/10/20 10:27
 */
object DeviceMangerDispose {

    lateinit var mViewModel: DeviceViewModel

    fun init(application: Application) {
        mViewModel = ViewModelProvider.AndroidViewModelFactory.getInstance(application)
            .create(DeviceViewModel::class.java)
        if (MrkDeviceManger.isLocalData) {
            mViewModel.initLocalData()
        }
    }

    /**
     * 所有连接过的设备对象
     * @param key 设备大类
     */
    private val deviceMap = HashMap<String, DeviceMangerBean>()


    /**
     * 所有的监听
     */
    private var allListener = HashMap<String, DeviceListener>()


    /**
     * 我绑定的所有设备数据
     */
    private var deviceMyBindBeanList = ArrayList<DeviceMyBindBean.Record>()

    /**
     * 把连接成功的设备设备保存起来
     */
    fun addDeviceConnect(bean: DeviceGoConnectBean) {
        if (getDeviceMangerBean(bean.mac) == null) {
            deviceMap[bean.productId] = DeviceMangerBean(connectBean = bean)
        }
    }


    /**
     * 监听所有设备的状态
     */
    fun addDeviceListener(context: Context, listener: DeviceListener) {
        allListener[context.javaClass.name] = listener
    }

    fun getDeviceListener(): HashMap<String, DeviceListener> {
        return allListener
    }


    /**
     * 监听蓝牙开关状态
     */
    fun registerBluetoothStateListener(onBluetoothStatus: (BluetoothEnum) -> Unit) {
        if (getBluetoothStatus()) {
            // 蓝牙已打开
            onBluetoothStatus.invoke(BluetoothEnum.OPEN)
            showLog("蓝牙已打开")
        } else {
            // 蓝牙未打开
            onBluetoothStatus.invoke(BluetoothEnum.CLOSE)
            showLog("蓝牙未打开")
        }
        BluetoothManager.registerBluetoothStateListener { boolean ->
            showLog("收到蓝牙开关监听:${boolean}")
            onBluetoothStatus.invoke(if (boolean) BluetoothEnum.OPEN else BluetoothEnum.CLOSE)
            //收到蓝牙关闭监听  则把所有的设备连接状态设置为未连接
            if (!boolean) {
                deviceMap.values.forEach {
                    it.deviceDetails?.let {
                        setDeviceStatus(false, it.mac, DeviceConnectEnum.OFF)
                    }
                }
            }
        }
    }


    /**
     * 创建
     * @param params 可以是设备大类  也可以mac地址
     */
    fun create(
        context: FragmentActivity,
        params: String
    ): DeviceControl {
        return DeviceControl(
            context,
            getDeviceGoConnectBean(params)
        )
    }

    /**
     * 创建
     */
    fun create(
        context: FragmentActivity,
        bean: DeviceGoConnectBean
    ): DeviceControl {
        return DeviceControl(
            context,
            bean
        )
    }


    /**
     * 开始搜索设备
     * @param bluetoothName 根据蓝牙名匹配指定单个设备名称
     * @param productId 产品id
     * @param modelId 型号id
     * @param notShowProductIdList 不显示的产品id集合
     * @param showBindDevice 是否展示已绑定设备 1显示 0不显示
     * @param showType 显示类型，1显示运动设备，3显示健康设备
     * @param onSearchStatus 搜索状态回调
     * @param onSearchDevice 搜索到的设备回调
     * @param onForwardToSettings 第二次拒绝授权后的弹窗
     * @param onPermissionError 授权失败
     */
    fun startSearch(
        context: FragmentActivity,
        bluetoothName: String = "",
        productId: String? = null,
        modelId: String? = null,
        showType: Int? = null,
        notShowProductIdList: IntArray? = null,
        showBindDevice: Int? = null,
        onSearchStatus: ((BluetoothEnum) -> Unit),
        onSearchDevice: ((DeviceSearchBean) -> Unit),
        onBluetoothClose: (() -> Unit)? = null,
        onForwardToSettings: ((scope: ForwardScope, deniedList: List<String>) -> Unit)? = null,
        onPermissionError: ((grantedList: List<String>) -> Unit)? = null
    ) {
        checkPermission(
            context,
            onBluetoothClose,
            onForwardToSettings,
            onSuccess = {
                onSearchStatus.invoke(BluetoothEnum.START)
                val uniqueData = mutableSetOf<String>()
                BluetoothManager.startSearch({ resultName, resultMac ->
                    if (resultName.isNullOrEmpty() && resultMac.isNullOrEmpty()) {
                        onSearchStatus.invoke(BluetoothEnum.STOP)
                    } else {
                        onSearchStatus.invoke(BluetoothEnum.ING)
                        // 检查数据是否已存在
                        if (!uniqueData.contains(resultMac)) {
                            // 数据不存在，添加到集合中
                            uniqueData.add(resultMac)
                            mViewModel.getSearchDeviceBean(
                                bluetoothName = resultName,
                                mac = resultMac,
                                productId = productId,
                                modelId = modelId,
                                showType = showType,
                                notShowProductIdList = notShowProductIdList,
                                showBindDevice = showBindDevice
                            ) {
                                onSearchDevice.invoke(it)
                                //如果是通过蓝牙名称搜索 搜索到则要停止搜索
                                if (bluetoothName == resultName) {
                                    BluetoothManager.stopSearch()
                                    onSearchStatus.invoke(BluetoothEnum.STOP)
                                }
                            }
                        }
                    }
                }, bluetoothName)
            }, onPermissionError = {
                onPermissionError?.invoke(it)
                onSearchStatus.invoke(BluetoothEnum.STOP)
            })
    }


    /**
     * 校验权限
     * @param onSuccess 权限通过
     * @param onBluetoothClose 判断蓝牙开关是否打开打开 如果传入的有值则自己处理,如果没值则自动打开
     * @param onForwardToSettings 第二次拒绝授权后的弹窗
     * @param onPermissionError 授权失败
     */
    fun checkPermission(
        context: FragmentActivity,
        onBluetoothClose: (() -> Unit)? = null,
        onForwardToSettings: ((scope: ForwardScope, deniedList: List<String>) -> Unit)? = null,
        onPermissionError: ((grantedList: List<String>) -> Unit),
        onSuccess: (() -> Unit)
    ) {
        DevicePermissionUtils.blueRequest(
            context,
            onForwardToSettings = onForwardToSettings,
            onCallBack = { allGranted, grantedList, deniedList ->
                //allGranted 权限是否全部通过
                //grantedList 通过名单
                //deniedList 拒绝名单
                //授权全部通过
                if (allGranted) {
                    //判断是否开启了蓝牙开关
                    if (!getBluetoothStatus()) {
                        //蓝牙关闭 不需要自己处理
                        if (onBluetoothClose == null) {
                            showLog("开启蓝牙")
                            openBluetooth(context) {
                                onSuccess.invoke()
                                showLog("开启成功")
                            }
                        } else {
                            //蓝牙关闭需要自己处理
                            onBluetoothClose.invoke()
                        }
                    } else {
                        onSuccess.invoke()
                    }
                } else {
                    showLog("权限获取失败:${deniedList}")
                    onPermissionError.invoke(deniedList)
                }

            })
    }

    /**
     * 关闭搜索
     */
    fun stopSearch() {
        BluetoothManager.stopSearch()
    }

    /**
     *  手机蓝牙开关转态
     */
    fun getBluetoothStatus(): Boolean {
        return BluetoothManager.isBluetoothOpened()
    }

    /**
     *  打开蓝牙
     */
    fun openBluetooth(
        context: FragmentActivity,
        onBluetoothStatusListener: ((Boolean) -> Unit)? = null
    ) {
        BluetoothManager.openBluetooth(context, onBluetoothStatusListener)
    }


    /**
     * 获取保存的对象
     * @param params 可以是设备大类  也可以mac地址 也可以是蓝牙名称
     */
    fun getDeviceMangerBean(params: String): DeviceMangerBean? {
        var bean: DeviceMangerBean? = null
        //如果是mac地址
        if (isMac(params)) {
            for (value in deviceMap.values) {
                if (value.connectBean.mac == params) {
                    bean = value
                    break
                }
            }
        } else {
            //用设备大类去匹配
            bean = deviceMap[params]
            //如果设备大类匹配不到 则可能传进来的值是蓝牙名称
            if (bean == null) {
                for (value in deviceMap.values) {
                    if (value.connectBean.bluetoothName == params) {
                        bean = value
                        break
                    }
                }
            }
        }
        return bean
    }


    /**
     * 判断当前是否有连接
     * @param params 可以是设备大类  也可以mac地址
     */
    fun isConnect(params: String): Boolean {
        return if (params.isEmpty()) false else BluetoothManager.getDeviceBean(
            params
        ).isConnect
    }


    /**
     * 设置当前设备的状态
     */
    fun setDeviceStatus(
        isAuto: Boolean,
        mac: String,
        status: DeviceConnectEnum,
    ) {
        getDeviceMangerBean(mac)?.run {
            this.connectEnum = status
            getDeviceListener().values.forEach {
                it.onConnectStatus(isAuto, this)
            }
        }
    }


    /**
     * 获取设备状态
     */
    fun getDeviceStatus(params: String): DeviceConnectEnum {
        return getDeviceMangerBean(params)?.connectEnum ?: DeviceConnectEnum.OFF
    }

    fun showLog(msg: String) {
        msg.log("BaseDeviceManger")
    }

    /**
     * 断开连接
     * @param  params 可以是设备大类  也可以mac地址
     */
    fun disConnect(params: String, onSuccess: ((mac: String) -> Unit)? = null) {
        if (isMac(params)) {
            BluetoothManager.disConnect(params, onSuccess)
        } else {
            getDeviceMangerBean(params)?.run {
                BluetoothManager.disConnect(this.connectBean.mac, onSuccess)
            }
        }
    }


    /**
     * 解绑设备
     * @param deviceUserRelId 用户设备关联id
     */
    fun unBindDevice(
        context: FragmentActivity,
        mac: String,
        deviceUserRelId: String,
        onSuccess: () -> Unit
    ) {
        mViewModel.unBindDevice(deviceUserRelId) {
            //如果已经连接 去解绑则先断开连接
            if (isConnect(mac)) {
                disConnect(mac) {
                    clear(context, mac)
                    onSuccess.invoke()
                }
            } else {
                clear(context, mac)
                onSuccess.invoke()
            }

        }
    }

    /**
     * 获取该用户所有绑定的设备
     * @param productType 产品分类 1.运动设备，3健康设备
     * @param productId 产品id
     */
    fun getDeviceMyBindList(
        productType: String = "",
        productId: String = "",
        onSuccess: (List<DeviceMyBindBean.Record>) -> Unit
    ) {
        mViewModel.getDeviceMyBindBean(productType, productId) {
            deviceMyBindBeanList.clear()
            deviceMyBindBeanList.addAll(it)
            onSuccess.invoke(it)
        }
    }

    /**
     * 通过传入的mac地址获取绑定的设备
     * @param mac 地址
     */
    fun getDeviceMyBindBean(mac: String): DeviceMyBindBean.Record? {
        var result: DeviceMyBindBean.Record? = null
        for (i in 0 until deviceMyBindBeanList.size) {
            val bean = deviceMyBindBeanList[i]
            if (bean.mac == mac) {
                result = bean
                break
            }
        }
        return result
    }


    /**
     * 释放数据
     * @param params 可以是设备大类  也可以mac地址 (为空则表示释放所有)
     *
     */
    fun clear(context: Context, params: String? = null) {

        if (params == null) {
            showLog("释放所有数据")
            deviceMap.values.forEach {
                disConnect(it.connectBean.mac)
                it.deviceFunction?.run {
                    this.clearData()
                    this.disConnectReset()
                    this.end()
                }
                it.deviceOtaBean = null
                it.deviceDetails = null
                it.deviceFunction = null
            }
            deviceMap.clear()
            getDeviceListener().clear()
        } else {
            getDeviceMangerBean(params)?.run {
                this.deviceFunction?.run {
                    showLog("释放数据")
                    this.clearData()
                    this.disConnectReset()
                    this.end()
                }
                this.deviceFunction = null
            }
            getDeviceListener().remove(context.javaClass.name)
        }

    }


    /**
     * 判断当前传入的字段 是否为mac地址
     */
    private fun isMac(params: String): Boolean {
        //直接通过传入的值来判断 中间有没有:号,有就是mac地址
        return params.contains(":")
    }

    /**
     * 获取所有保存的对象
     */
    fun getDeviceMap(): HashMap<String, DeviceMangerBean> {
        return deviceMap
    }


    /**
     * 获取设备的可连接对象
     */
    fun getDeviceGoConnectBean(params: String): DeviceGoConnectBean? {
        //先去连接的对象里面取
        var bean: DeviceGoConnectBean? = getDeviceMangerBean(params)?.connectBean

        //没有的话从绑定的列表里面去取
        if (bean == null) {
            val bindBean = MrkDeviceManger.getDeviceMyBindBean(params)
            if (bindBean != null) {
                bean = DeviceGoConnectBean(
                    bindBean.mac,
                    bindBean.productId,
                    bindBean.bluetoothName,
                    bindBean.modelId,
                    bindBean.uniqueModelIdentify
                )
            }
        }

        return bean
    }
}