package com.mrk.device.device

import android.Manifest
import android.os.Build
import androidx.fragment.app.FragmentActivity
import com.permissionx.guolindev.PermissionX
import com.permissionx.guolindev.request.ForwardScope
import com.v.log.util.log
import com.v.log.util.logE

object DevicePermissionUtils {

    fun blueRequest(
        context: FragmentActivity,
        onForwardToSettings: ((scope: ForwardScope, deniedList: List<String>) -> Unit)? = null,
        onCallBack: ((allGranted: <PERSON><PERSON><PERSON>, grantedList: List<String>, deniedList: List<String>) -> Unit)
    ) {
        PermissionX.init(context)
            .permissions(getBLEPermissions().asList())
            .onForwardToSettings { scope, deniedList ->
                onForwardToSettings?.invoke(scope, deniedList)
            }
            .request { allGranted, grantedList, deniedList ->
                onCallBack.invoke(allGranted, grantedList, deniedList)
                if (!allGranted) {
                    "PermissionX Error:${deniedList}".logE("PermissionX")
                }
            }
    }

    /**
     * 设备设备所需权限
     */
    private fun getBLEPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            //在大于等12的时候
            //申请权限要么加上 Manifest.permission.ACCESS_FINE_LOCATION // 添加这行确保所有情况下蓝牙扫描都能正常工作
            //要么在不申请定位权限时,必须加上android:usesPermissionFlags="neverForLocation"，否则搜不到设备
            //<uses-permission
            //android:name="android.permission.BLUETOOTH_SCAN"
            //android:usesPermissionFlags="neverForLocation"
            //tools:targetApi="s"  />
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.ACCESS_FINE_LOCATION // 添加这行确保所有情况下蓝牙扫描都能正常工作
            )
        } else {
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
            )
        }
    }

}