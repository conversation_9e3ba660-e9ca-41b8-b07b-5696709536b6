package com.mrk.device.device


import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.cc.control.BaseDeviceFunction
import com.cc.control.BluetoothManager
import com.cc.control.BluetoothManager.sentryPushListener
import com.cc.control.bean.DevicePropertyBean
import com.cc.control.bean.DeviceTrainBO
import com.cc.control.enums.PushLogLevel
import com.cc.control.enums.TreadmillStatus
import com.cc.control.ota.OtaListener
import com.cc.control.protocol.DeviceConstants
import com.cc.control.protocol.getDeviceFunction
import com.cc.control.protocol.getDeviceOtaFunction
import com.cc.control.vbToJson
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.BluetoothEnum
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceDetailsBean
import com.mrk.device.bean.DeviceGoConnectBean
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceOtaBean
import com.mrk.device.bean.DeviceTreadmillEnum
import com.mrk.device.listener.DeviceOtaListener
import com.mrk.device.listener.DownloadListener
import com.mrk.device.utils.DownloadUtil
import com.permissionx.guolindev.request.ForwardScope
import com.v.log.util.log
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.roundToInt


/**
 * author  : ww
 * desc    : 设备控制
 * time    : 2024/1/15 16:29
 */
class DeviceControl(
    private val context: FragmentActivity,
    private val goConnectBean: DeviceGoConnectBean? = null,
) : DefaultLifecycleObserver, DeviceListener() {


    private var mDeviceFunction: BaseDeviceFunction? = null


    //检查Ota  只有在连接成功的时候才去验证
    private var isOta = false

    // 是否需要开启数据通道(设备实时返回数据)
    private var isOpenDataChannel = false

    //设备断开是否需要自动重连
    private var isAutoReconnect: Boolean = false

    //跑步机当前状态
    private var treadmillStatus = DeviceTreadmillEnum.WAIT

    //当前的监听
    private var listener: DeviceListener? = null

    //是否发送了跑步机启动指令(t19两次启动会暂停)
    private var lastTime = 0L

    init {
        context.lifecycle.addObserver(this)
        //设备断开状态监听
        BluetoothManager.registerDisConnectListener {
            DeviceMangerDispose.showLog("收到设备断开:${it}")


            DeviceMangerDispose.getDeviceMangerBean(it)?.run {
                this.deviceFunction?.disConnectReset()
                this.deviceFunction = null

                if (deviceFunction == mDeviceFunction) {
                    mDeviceFunction = null
                }

                //只有在不是ERROR的时候才去发送失败,因为连接的时候,设备接口失败会同步调用断开设备,而这里需要返回ERROR
                if (connectEnum != DeviceConnectEnum.ERROR) {
                    DeviceMangerDispose.setDeviceStatus(false, it, DeviceConnectEnum.OFF)

                    if (it == goConnectBean?.mac) {
                        listener?.onConnectStatus(false, this)
                    }
                }

                //如果当前断开的设备已经开启了自动重连 则重新连接
                if (isAutoReconnect && DeviceMangerDispose.getBluetoothStatus() && it == goConnectBean?.mac) {
                    showLog("自动重连")
                    goConnect(true)
                }
            }

        }

    }

    private fun registerDeviceDispose(isAuto: Boolean = false): DeviceControl? {
        if (goConnectBean == null) {
            return null
        }

        //如果设备未连接 则不需要注册
        if (!DeviceMangerDispose.isConnect(goConnectBean.mac)) {
            return this
        }
        DeviceMangerDispose.getDeviceMangerBean(goConnectBean.mac)?.run {
            val mangerBean = this
            if (deviceFunction == null) {
                showLog("新建deviceFunction ")
                deviceFunction = getDeviceFunction(goConnectBean.productId)
            }

            //拿到服务器的单位
            val netUnit = mangerBean.deviceDetails?.productModelTsl?.unitVal ?: 1

            showLog("注册设备回调状态")
            deviceFunction?.run {
                initDevice()
                registerDataListener(dataListener = {
                    //如果设备返回的单位为-1 则直接拿去服务器返回的
                    if (it.unitDistance == -1) {
                        it.unitDistance = netUnit
                    }
                    onNotifyData(it)
                }, unitDistanceListener = {
                    if (unitDistance == -1) {
                        unitDistance = it
                        showLog("设备数据单位:${it}")
                    }
                    sentryPushListener?.invoke(PushLogLevel.INFO, "ble_getUnit_callback", mapOf("unitDistance" to it).vbToJson()
                    )
                })

                //只有跑步机才会有设备状态
                if (goConnectBean.productId == DeviceConstants.D_TREADMILL) {
                    registerStatusListener { code ->
                        val status = when (code) {
                            TreadmillStatus.DEVICE_TREADMILL_RUNNING.num -> DeviceTreadmillEnum.START
                            TreadmillStatus.DEVICE_TREADMILL_STOP.num -> DeviceTreadmillEnum.STOP
                            TreadmillStatus.DEVICE_TREADMILL_PAUSE.num -> DeviceTreadmillEnum.PAUSE
                            TreadmillStatus.DEVICE_TREADMILL_AWAIT.num -> DeviceTreadmillEnum.WAIT
                            TreadmillStatus.DEVICE_TREADMILL_LAUNCHING.num -> DeviceTreadmillEnum.COUNT_TIME
                            TreadmillStatus.DEVICE_TREADMILL_COUNTDOWN.num -> DeviceTreadmillEnum.SLOW_DOWN
                            TreadmillStatus.DEVICE_TREADMILL_MALFUNCTION.num -> DeviceTreadmillEnum.DISABLE
                            TreadmillStatus.DEVICE_TREADMILL_DISABLE.num -> DeviceTreadmillEnum.DISABLE
                            else -> {
                                DeviceTreadmillEnum.DISABLE
                            }
                        }

                        onDeviceTreadmillStatus(status)
                        treadmillStatus = status
                    }
                }
                registerNotify()
                //获取单位
                getUnitDistance()

                if (unitDistance == -1) {
                    //这里加个2秒延迟 如果获取不到设备的单位 则拿服务配置的单位去获取ota
                    context.lifecycleScope.launch {
                        delay(2000)
                        if (unitDistance == -1) {
                            unitDistance = netUnit
                            showLog("后台数据单位:${netUnit}")
                        }
                        deviceOtaDispose(mangerBean.deviceDetails, unitDistance, isAuto)
                    }
                } else {
                    deviceOtaDispose(mangerBean.deviceDetails, unitDistance, isAuto)
                }

            }

            mDeviceFunction = deviceFunction

            if (isOpenDataChannel) {
                openDataChannel()
            }
        }
        return this
    }


    /**
     * 设置设备回调监听器
     */
    fun setOnDeviceListener(listener: DeviceListener): DeviceControl {
        this.listener = listener
        return this
    }

    /**
     * 注册设备回调状态
     */
    fun registerDevice(): DeviceControl? {
        isOpenDataChannel = true
        return registerDeviceDispose()
    }

    /**
     * 处理设备ota
     * @param unit 设备单位
     * @param isAuto 此次连接是自动重连 还是手动连接,用来返回给客户端的状态
     */
    private fun deviceOtaDispose(
        deviceDetails: DeviceDetailsBean?,
        unit: Int,
        isAuto: Boolean = false
    ) {
        if (goConnectBean == null) {
            return
        }

        if (deviceDetails == null || !isOta) {
            return
        }

        showLog("检测Ota")
        if (MrkDeviceManger.isLocalData) {
            showLog("设备${goConnectBean.bluetoothName}成功(设备/详情接口/都成功)")
            setDeviceStatus(isAuto, DeviceConnectEnum.ON)
        } else {
            val connectName = if (isAuto) "重连" else "连接"
            if (deviceDetails.productModelTsl.isOta == 0 || isAuto) {
                //连接成功(真正的成功,设备与接口都成功了)
                if (isAuto) {
                    showLog("设备${connectName}成功(设备/详情接口/不用检查ota/成功)")
                } else {
                    showLog("设备${connectName}成功(设备/详情接口/接口配置不用检查ota/成功)")
                }
                setDeviceStatus(isAuto, DeviceConnectEnum.ON)
                return
            }

            BluetoothManager.readOtaVersion(
                type = goConnectBean.productId,
                eigenValue = deviceDetails.versionEigenValue
            ) { modelNumber, modelRevision ->
                //把设备ota对象 存起来

                DeviceMangerDispose.mViewModel.getDeviceOtaBean(
                    deviceDetails,
                    unit, modelNumber, modelRevision, onSuccess = {
                        it.currentVersionNumber = modelRevision
                        onDeviceOta(it)
                        //连接成功(真正的成功,设备与接口都成功了)
                        showLog("设备${connectName}成功(设备/详情接口/OTA/都成功)")
                        setDeviceStatus(isAuto, DeviceConnectEnum.ON)
                        getDeviceMangerBean()?.deviceOtaBean = it
                    }, onError = {
                        showLog("设备${connectName}成功(设备/详情接口/成功,OTA失败)")
                        //连接失败了 需要断开已经连接的设备
                        setDeviceStatus(isAuto, DeviceConnectEnum.ERROR)
                        disConnect()
                    }
                )


            }
        }

    }

    /**
     * 获取设备状态
     */
    fun getDeviceStatus(): DeviceConnectEnum {
        if (goConnectBean == null) {
            return DeviceConnectEnum.OFF
        }
        return DeviceMangerDispose.getDeviceMangerBean(goConnectBean.mac)?.connectEnum
            ?: DeviceConnectEnum.OFF
    }

    /**
     * 设置当前设备的状态
     * @param isAuto 此次设置状态是自动重连 还是正常连接
     */
    private fun setDeviceStatus(isAuto: Boolean, status: DeviceConnectEnum) {
        getDeviceMangerBean()?.run {
            this.connectEnum = status
            onConnectStatus(isAuto, this)
        }
    }

    /**
     * 获取设备对象
     */
    fun getDeviceMangerBean(): DeviceMangerBean? {
        if (goConnectBean == null) {
            return null
        }
        return DeviceMangerDispose.getDeviceMangerBean(goConnectBean.mac)
    }

    /**
     * 获取设备详情
     */
    fun getDeviceDetails(): DeviceDetailsBean? {
        if (goConnectBean == null) {
            return null
        }
        return DeviceMangerDispose.getDeviceMangerBean(goConnectBean.mac)?.deviceDetails
    }

    /**
     * 获取设备OTA数据
     */
    fun getDeviceOtaBean(): DeviceOtaBean? {
        if (goConnectBean == null) {
            return null
        }
        return DeviceMangerDispose.getDeviceMangerBean(goConnectBean.mac)?.deviceOtaBean
    }

    /**
     *
     */
    fun connect() {
        connect(
            onAutoConnect = null,//返回的值为当前已经连接的设备
            onBluetoothClose = null,
            onForwardToSettings = null,
            onPermissionError = null
        )
    }

    /**
     * 连接设备
     * @param onAutoConnect 是否为自动处理已经连接的设备  如果当前设备大类已经连接且onAutoConnect不传 则直接断开已经连接的设备,再连接新的,如果为有值则自己处理
     * @param onBluetoothClose 蓝牙关闭是否需要回调(如果传入有值则表示自己处理,没值则自动处理,自动处理为先断开再连接)
     * @param onPermissionError 权限获取失败
     */
    fun connect(
        onAutoConnect: ((DevicePropertyBean) -> Unit)? = null,//返回的值为当前已经连接的设备
        onBluetoothClose: (() -> Unit)? = null,
        onForwardToSettings: ((scope: ForwardScope, deniedList: List<String>) -> Unit)? = null,
        onPermissionError: ((grantedList: List<String>) -> Unit)? = null,
    ) {

        if (goConnectBean == null) {
            return
        }

        DeviceMangerDispose.showLog("蓝牙连接:${goConnectBean}")
        DeviceMangerDispose.checkPermission(
            context,
            onBluetoothClose,
            onForwardToSettings,
            onSuccess = {
                val connectBean = BluetoothManager.getDeviceBean(goConnectBean.productId)
                //如果mac地址没有值 则有可能是先用苹果设备连接而导致没有mac地址 所以这里需要先搜索 重新做一次处理
                //并且当前类型没有设备连接
                if (goConnectBean.mac.isNullOrEmpty() && !connectBean.isConnect) {
                    DeviceMangerDispose.addDeviceConnect(goConnectBean)
                    val bluetoothName = goConnectBean.bluetoothName
                    DeviceMangerDispose.setDeviceStatus(
                        false,
                        goConnectBean.productId,
                        DeviceConnectEnum.ING
                    )
                    DeviceMangerDispose.showLog("没有mac地址,搜索蓝牙名称为:${bluetoothName}的设备进行连接")
                    var searchMac = ""
                    DeviceMangerDispose.startSearch(
                        context,
                        showBindDevice = 1,
                        bluetoothName = bluetoothName,
                        onSearchStatus = {
                            if (it == BluetoothEnum.STOP) {
                                if (searchMac.isNullOrEmpty()) {
                                    DeviceMangerDispose.showLog("没有mac地址,未搜索得到蓝牙名称为:${bluetoothName}的设备")
                                    DeviceMangerDispose.setDeviceStatus(
                                        false,
                                        goConnectBean.productId,
                                        DeviceConnectEnum.ERROR
                                    )
                                } else {
                                    DeviceMangerDispose.showLog("没有mac地址,搜索到了蓝牙名称为:${bluetoothName}的设备")
                                    goConnectBean.mac = searchMac
                                    goConnect()
                                }
                            }
                        },
                        onSearchDevice = {
                            searchMac = it.mac
                        })
                } else {
                    //在连接的时候 关闭蓝牙搜索
                    DeviceMangerDispose.stopSearch()
                    if (connectBean.isConnect && goConnectBean.mac == connectBean.address && isConnect()) {
                        setDeviceStatus(false, DeviceConnectEnum.ON)
                        showLog("设备已经连接 无需重复连接")
                        return@checkPermission
                    }
                    //如果已经有设备连接 //判断是否为自动连接 如果是则先断开 再连接
                    if (connectBean.isConnect) {
                        if (onAutoConnect == null) {
                            DeviceMangerDispose.showLog("先断开已有连接:${connectBean.address}")
                            unAutoConnect()
                            DeviceMangerDispose.disConnect(connectBean.address) {
                                context.lifecycleScope.launch {
                                    //延迟500毫秒再去调用连接 是因为怕还没有断开就去连接了 而后再收到上一个断开的回调
                                    delay(500)
                                    DeviceMangerDispose.addDeviceConnect(goConnectBean)
                                    goConnect()
                                }
                            }
                        } else {
                            onAutoConnect.invoke(connectBean)
                        }
                    } else {
                        DeviceMangerDispose.addDeviceConnect(goConnectBean)
                        goConnect()
                    }
                }
            }, onPermissionError = {
                onPermissionError?.invoke(it)
            })
    }


    /**
     * 断开当前连接
     */
    fun disConnect() {
        if (goConnectBean == null) {
            return
        }
        //手动断开设备则关闭自动连接
        unAutoConnect()
        DeviceMangerDispose.disConnect(goConnectBean.mac)
    }

    /**
     * 解绑设备
     */
    fun unBindDevice(onSuccess: () -> Unit) {
        getDeviceDetails()?.run {
            DeviceMangerDispose.unBindDevice(context, this.mac, this.deviceUserRelId, onSuccess)
        }

    }

    /**
     * 当前设备是否连接
     */
    fun isConnect(): Boolean {
        if (goConnectBean == null) {
            return false
        }
        return DeviceMangerDispose.isConnect(goConnectBean.mac)
    }

    /**
     * 获取当前设备是否运行
     */
    fun isRunning(): Boolean {
        return if (mDeviceFunction == null) {
            false
        } else {
            mDeviceFunction!!.isRunning()
        }
    }

    /**
     * 连接设备
     * @param isAuto 此次连接是否是断开以后得自动连接(需要先调用 [autoConnect])
     */
    private fun goConnect(isAuto: Boolean = false) {
        if (goConnectBean == null) {
            return
        }

        val connectName = if (isAuto) "重连" else "连接"
        //把底层库的mac地址赋值一下
        BluetoothManager.getDeviceBean(goConnectBean.productId).address = goConnectBean.mac
        setDeviceStatus(isAuto, DeviceConnectEnum.ING)
        showLog("开始${connectName}设备")
        BluetoothManager.connect(
            goConnectBean.mac,
            goConnectBean.productId,
            goConnectBean.bluetoothName,
            connectListener = { isConnect, characteristicBeans ->
                //连接成功了 则前往调取接口
                if (isConnect) {
                    DeviceMangerDispose.mViewModel.connectDevice(
                        goConnectBean,
                        characteristicBeans,
                        onSuccess = { bean ->
                            BluetoothManager.initProperty(
                                bean.productId,
                                bean.communicationProtocol
                            )
                            //把设备详情对象 存起来
                            DeviceMangerDispose.getDeviceMangerBean(if (bean.mac.isNullOrEmpty()) goConnectBean.mac else bean.mac)
                                ?.deviceDetails = bean

                            isOta = true
                            registerDeviceDispose(isAuto)
                        },
                        onError = {
                            showLog("设备${connectName}失败(设备蓝牙成功,接口失败)")
                            //连接失败了 需要断开已经连接的设备
                            setDeviceStatus(isAuto, DeviceConnectEnum.ERROR)
                            disConnect()
                        })

                } else {
                    setDeviceStatus(isAuto, DeviceConnectEnum.ERROR)
                    showLog("设备${connectName}蓝牙错误")
                }

            }
        )
    }

    /**
     * 开启数据通道(设备实时返回数据)
     */
    private fun openDataChannel(): DeviceControl {
        showLog("开启数据通道")
        context.lifecycleScope.launch {
            delay(500)
            mDeviceFunction?.start()
        }
        return this
    }


    /**
     * 设备数据清空
     */
    fun clearData(onSuccess: (() -> Unit)? = null) {
        if (getDeviceDetails()?.productModelTsl?.isClean == 1) {
            mDeviceFunction?.run {
                showLog("发送控制指令:设备数据清空")
                this.clearData(onSuccess)
            }
        }
    }

    /**
     * 设备数据清空
     */
    fun clearData() {
        clearData(null)
    }


    /**
     * 发送控制指令 设备开始
     * 如果没有数据回调 请检查是否调用了 [registerDevice]方法
     */
    fun deviceStart() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastTime > 3000) {
            mDeviceFunction?.run {
                treadmillStart()
                showLog("发送控制指令:设备开始")
            }
            lastTime = System.currentTimeMillis()
        }
    }

    /**
     * 发送控制指令 设备暂停(跑步机独有)
     */
    fun devicePause() {
        mDeviceFunction?.run {
            showLog("发送控制指令:设备暂停(跑步机独有)")
            treadmillPause()
        }
    }


    /**
     * 设置设备数据是否回调(默认开启)
     */
    fun setNotifyData(boolean: Boolean) {
        mDeviceFunction?.run {
            if (boolean) {
                registerNotify()
            } else {
                unRegisterNotify()
            }
        }
    }


    /**
     * 设置模式(跳绳独有)
     */
    fun setModel(model: Int = 0, targetNum: Int = 0, onSuccess: (() -> Unit) = {}) {
        mDeviceFunction?.setMode(model, targetNum, onSuccess)
    }


    /**
     * 发送控制指令
     * 发送阻力
     */
    fun sendCommandResistance(resistance: Int) {
        sendCommand(resistance = resistance)
    }

    /**
     * 发送控制指令
     * 发送速度 发送坡度(必须是组合指令)
     */
    fun sendCommandSlope(slope: Int) {
        sendCommand(slope = slope)
    }


    /**
     * 发送跑步机控制指令
     * 发送速度 发送坡度(必须是组合指令)
     */
    fun sendCommandTreadmill(speed: Int, slope: Int) {
        sendCommand(speed = speed, slope = slope)
    }


    /**
     * 发送控制指令
     * @param speed 跑步机专属 如果跑步机在待机状态，会先启动再发送指令，中间5秒的间隔
     * @param slope 坡度
     * @param resistance 阻力
     * @param isCheck 是否需要校验最大最小值
     */
    fun sendCommand(
        resistance: Int = -1,
        speed: Number = -1,
        slope: Int = -1,
        isCheck: Boolean = true
    ) {

        if (goConnectBean == null) {
            return
        }

        when (goConnectBean.productId) {
            DeviceConstants.D_BICYCLE,
            DeviceConstants.D_TECHNOGYM,
            DeviceConstants.D_ROW,
            DeviceConstants.D_POWER,
                -> {

                if (resistance == -1) {
                    return
                }

                var formatResistance = resistance
                var formatSlope = slope

                if (isCheck) {
                    //调节阻力
                    getDeviceDetails()?.productModelTsl?.run {
                        if (resistance < this.minResistance) {
                            //如果传入的值少于最小的值 则把传入的值改为最小的值
                            formatResistance = this.minResistance
                        } else if (resistance > this.maxResistance) {
                            //如果传入的值大于最大的值 则把传入的值改为最大的值
                            formatResistance = this.maxResistance
                        }
                    }

                    //调节坡度
                    getDeviceDetails()?.productModelTsl?.run {
                        if (this.controlSlope == 1) {
                            if (slope < this.minSlope) {
                                //如果传入的值少于最小的值 则把传入的值改为最小的值
                                formatSlope = this.minSlope
                            } else if (slope > this.maxSlope) {
                                //如果传入的值大于最大的值 则把传入的值改为最大的值
                                formatSlope = this.maxSlope
                            }
                        }
                    }

                }

                val controlSlope = getDeviceDetails()?.productModelTsl?.controlSlope
                mDeviceFunction?.run {
                    setControl(resistance = formatResistance)
                    "调节阻力:$formatResistance".log("BaseDeviceManger")

                    if (formatSlope != -1 && controlSlope == 1) {
                        setControl(slope = formatSlope, isSlope = true)
                        "调节坡度:$formatSlope".log("BaseDeviceManger")
                    }

                }
            }

            DeviceConstants.D_TREADMILL -> {
                if (speed == -1 && slope == -1) {
                    return
                }

                if (treadmillStatus == DeviceTreadmillEnum.SLOW_DOWN || treadmillStatus == DeviceTreadmillEnum.COUNT_TIME) {
                    "跑步机正在启动中或者减速中,不响应任何指令".log("BaseDeviceManger")
                    return
                }

                var formatSpeed = speed.toFloat()
                var formatSlope = slope

                if (isCheck) {
                    //调节速度
                    getDeviceDetails()?.productModelTsl?.run {

                        if (formatSpeed < this.minSpeed) {
                            //如果传入的值少于最小的值 则把传入的值改为最小的值
                            formatSpeed = this.minSpeed.toFloat()
                        } else if (formatSpeed > this.maxSpeed) {
                            //如果传入的值大于最大的值 则把传入的值改为最大的值
                            formatSpeed = this.maxSpeed.toFloat()
                        }
                    }

                    //调节坡度
                    getDeviceDetails()?.productModelTsl?.run {
                        if (slope < this.minSlope) {
                            // 如果传入的值少于最小的值 则把传入的值改为最小的值
                            formatSlope = this.minSlope
                        } else if (slope > this.maxSlope) {
                            //如果传入的值大于最大的值 则把传入的值改为最大的值
                            formatSlope = this.maxSlope
                        }
                    }
                }

                if (mDeviceFunction?.isRunning() == false) {
                    treadmillStatus = DeviceTreadmillEnum.COUNT_TIME
                    //如果跑步机在待机状态 则先启动,间隔5秒后再发送指令
                    "跑步机在没有运行先启动".log("BaseDeviceManger")
                    deviceStart()
                    context.lifecycleScope.launch {
                        delay(5000)
                        mDeviceFunction?.run {
                            val speedFormat = (formatSpeed * 10).roundToInt()
                            "间隔5秒后再发送指令 调节速度:$speedFormat  调节坡度:$formatSlope".log("BaseDeviceManger")
                            //跑步机档位需要*10
                            setControl(speed = speedFormat, slope = formatSlope)
                        }
                    }

                } else {
                    mDeviceFunction?.run {
                        val speedFormat = (formatSpeed * 10).roundToInt()

                        "调节速度:$speedFormat  调节坡度:$formatSlope".log("BaseDeviceManger")
                        //跑步机档位需要*10
                        setControl(speed = speedFormat, slope = formatSlope)
                    }
                }
            }
        }
    }

    private fun showLog(msg: String) {
        if (goConnectBean == null) {
            return
        }
        DeviceMangerDispose.showLog("$msg: ${DeviceMangerDispose.getDeviceMangerBean(goConnectBean.mac)}")
    }


    /**
     * 销毁事件
     */
    fun clear() {
        if (goConnectBean == null) {
            return
        }
        unAutoConnect()
        DeviceMangerDispose.clear(context, goConnectBean.mac)
    }


    /**
     * 开启自动重连
     */
    fun autoConnect(): DeviceControl {
        showLog("开启自动重连配置")
        this.isAutoReconnect = true
        return this
    }

    /**
     * 关闭自动重连
     */
    private fun unAutoConnect() {
        if (isAutoReconnect) {
            showLog("关闭自动重连配置")
            this.isAutoReconnect = false
        }
    }

    /**
     * 获取跑步机状态
     */
    fun getTreadmillStatus(): DeviceTreadmillEnum {
        return treadmillStatus
    }

    /**
     * ota更新
     */
    fun otaUpdate(otaListener: DeviceOtaListener) {
        if (goConnectBean == null) {
            return
        }
        DeviceMangerDispose.getDeviceMangerBean(goConnectBean.mac)?.run {
            val deviceDetailsBean = this.deviceDetails
            if (deviceOtaBean == null || deviceDetailsBean == null) {
                otaListener.onError()
                return
            }
            if (deviceOtaBean?.downloadLink?.isEmpty() == true || deviceOtaBean?.isLastVersion == 1) {
                otaListener.onTheLatestVersion()
                return
            }

            deviceOtaBean?.run {
                DownloadUtil.download(context, this.downloadLink, object : DownloadListener {
                    override fun onStart() {
                        otaListener.onStart()
                    }

                    override fun onDownloadOtaProgress(
                        readableOffset: String,
                        readableTotalLength: String,
                        speed: String,
                        progress: Int,
                    ) {
                        otaListener.onDownloadOtaProgress(
                            readableOffset,
                            readableTotalLength,
                            speed,
                            progress
                        )
                    }

                    override fun onDownloadOtaEnd(path: String) {
                        otaInstall(deviceDetailsBean, path, otaListener)
                    }


                    override fun onError() {
                        otaListener.onError()
                    }

                })

            }

        }
    }


    /**
     * ota安装
     */
    private fun otaInstall(bean: DeviceDetailsBean?, path: String, otaListener: DeviceOtaListener) {
        mDeviceFunction?.unRegisterNotify()
        showLog("ota安装")
        bean?.run {
            getDeviceOtaFunction(this.otaProtocol).run {
                create(
                    bean.productId,
                    bean.otaProtocol,
                    otaListener = object : OtaListener {
                        override fun onError() {
                            otaListener.onError()
                        }

                        override fun onFinish() {
                            otaListener.onInstallOtaEnd()
                            DeviceMangerDispose.mViewModel.saveDeviceOtaLog(
                                bean.deviceUserRelId,
                                bean.firmwareVersion
                            )
                        }

                        override fun onProgress(pro: Int) {
                            otaListener.onInstallOtaProgress(pro)
                        }

                    })

                startUpdate(path)
            }
        }
    }


    override fun onDeviceOta(bean: DeviceOtaBean) {
        this.listener?.onDeviceOta(bean)
        DeviceMangerDispose.getDeviceListener().values.forEach {
            it.onDeviceOta(bean)
        }
    }


    override fun onConnectStatus(isAutoReconnect: Boolean, bean: DeviceMangerBean) {
        this.listener?.onConnectStatus(isAutoReconnect, bean)
        DeviceMangerDispose.getDeviceListener().values.forEach {
            it.onConnectStatus(isAutoReconnect, bean)
        }
    }

    override fun onDeviceTreadmillStatus(status: DeviceTreadmillEnum) {
        showLog("跑步机状态:${status}")
        this.listener?.onDeviceTreadmillStatus(status)
        DeviceMangerDispose.getDeviceListener().values.forEach {
            it.onDeviceTreadmillStatus(status)
        }
    }


    override fun onNotifyData(bean: DeviceTrainBO) {
        this.listener?.onNotifyData(bean)
        DeviceMangerDispose.getDeviceListener().values.forEach {
            it.onNotifyData(bean)
        }
    }


    override fun onDestroy(owner: LifecycleOwner) {
        destroy()
    }

    //回调缓慢问题完美解决方案
    //只有走回收流程的时候（返回键）的那种onPause，isFinishing才为true
    //普通的切到后台或者另外一个Activity盖上来的是那个isFinishing是false
    private var isDestroyed = false

    private fun destroy() {
        if (isDestroyed) {
            return
        }
        // 回收资源
        isDestroyed = true
        clear()
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        if (context.isFinishing) {
            destroy()
        }
    }


}