package com.mrk.device.device

import com.cc.control.bean.DeviceTrainBO
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceOtaBean
import com.mrk.device.bean.DeviceTreadmillEnum


/**
 * author  : ww
 * desc    :
 * time    : 2023/10/20 10:48
 */
abstract class DeviceListener : BaseDeviceListener {

    /**
     * 设备ota对象
     */
    override fun onDeviceOta(bean: DeviceOtaBean) {

    }


    /**
     * 连接状态
     * @param isAutoReconnect 是否是自动重连
     */
    override fun onConnectStatus(isAutoReconnect: Boolean, bean: DeviceMangerBean) {

    }

    /**
     * 跑步机状态
     */
    override fun onDeviceTreadmillStatus(status: DeviceTreadmillEnum) {

    }

    /**
     * 设备数据回调
     */
    override fun onNotifyData(bean: DeviceTrainBO) {

    }


}