package com.mrk.device

import androidx.lifecycle.viewModelScope
import com.cc.control.BluetoothManager
import com.cc.control.bean.CharacteristicBean
import com.cc.control.enums.PushLogLevel
import com.cc.control.vbToJson
import com.mrk.device.bean.DeviceDetailsBean
import com.mrk.device.bean.DeviceGoConnectBean
import com.mrk.device.bean.DeviceMyBindBean
import com.mrk.device.bean.DeviceOtaBean
import com.mrk.device.bean.DeviceSearchBean
import com.mrk.device.utils.LocalDataUtil
import com.mrk.network.MrkViewModel
import com.mrk.network.net.MrkNetwork
import com.mrk.network.net.request
import com.v.log.util.log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * author  : ww
 * desc    :
 * time    : 2023/10/20 14:56
 */
class DeviceViewModel : MrkViewModel() {

    var localData = HashMap<String, ArrayList<DeviceSearchBean>>()

    fun initLocalData() {
        viewModelScope.launch(Dispatchers.IO) {
            localData = LocalDataUtil.readSearchData()
        }
    }

    /**
     * 搜索设备
     * @param bluetoothName 蓝牙广播名
     * @param mac mac地址
     * @param productId 产品id
     * @param modelId 型号id
     * @param notShowProductIdList 不显示的产品id集合
     * @param showBindDevice 是否展示已绑定设备 1显示 0不显示
     * @param showType 显示类型，1显示运动设备，3显示健康设备
     */
    fun getSearchDeviceBean(
        bluetoothName: String = "",
        mac: String = "",
        productId: String? = null,
        modelId: String? = null,
        showType: Int? = null,
        notShowProductIdList: IntArray? = null,
        showBindDevice: Int? = null,
        onSuccess: (DeviceSearchBean) -> Unit
    ) {
        if (MrkDeviceManger.isLocalData) {
            viewModelScope.launch {
                val list = LocalDataUtil.readSearch(bluetoothName)
                if (list.size > 0) {
                    list.log()
                    val bean = list[0].copy()
                    bean.mac = mac
                    bean.bluetoothName = bluetoothName
                    onSuccess.invoke(bean)
                }
            }
        } else {
            val map = mutableMapOf<String, Any>()
            map["bluetoothName"] = bluetoothName
            map["mac"] = mac
            showBindDevice?.run {
                map["showBindDevice"] = this
            }
            showType?.run {
                map["showType"] = this
            }
            productId?.run {
                map["productId"] = this
            }
            modelId?.run {
                map["modelId"] = this
            }
            notShowProductIdList?.run {
                map["notShowProductIdList"] = this
            }
            request<DeviceSearchBean>({
                MrkNetwork.instance.post("/app/product-model/searchProductModel", map)

            }, success = {
                val map = mapOf(
                    "deviceType" to it.productId,
                    "bleCommunicationProtocol" to it.communicationProtocol,
                    "bleMac" to it.mac,
                    "bleName" to it.bluetoothName,
                )
                BluetoothManager.sentryPushListener?.invoke(
                    PushLogLevel.INFO,
                    "ble_api_scan",
                    map.vbToJson()
                )
                onSuccess.invoke(it)
            }, showErrorToast = false)
        }

    }

    /**
     * 连接设备
     * @param characteristic     所有特征值数据
     */
    fun connectDevice(
        bean: DeviceGoConnectBean,
        characteristic: List<CharacteristicBean>,
        onSuccess: (DeviceDetailsBean) -> Unit,
        onError: (String) -> Unit
    ) {
        characteristic.log()
        if (MrkDeviceManger.isLocalData) {
            viewModelScope.launch {
                val detailsBean = LocalDataUtil.readConnect(bean, characteristic)
                if (detailsBean != null) {
                    detailsBean.mac = bean.mac
                    detailsBean.bluetoothName = bean.bluetoothName
                    onSuccess.invoke(detailsBean)
                } else {
                    onError.invoke("没有对应的协议")
                }
            }
        } else {
            request<DeviceDetailsBean>({
                MrkNetwork.instance.post(
                    "/app/device-user-rel/connectionDevice",
                    mapOf(
                        "mac" to bean.mac,
                        "bluetoothName" to bean.bluetoothName,
                        "productId" to bean.productId,
                        "modelId" to bean.modelId,
                        "characteristic" to characteristic,
                        "uniqueModelIdentify" to bean.uniqueModelIdentify,
                    )
                )
            }, success = {
                onSuccess(it)
                val map = mapOf(
                    "deviceType" to it.productId,
                    "bleCommunicationProtocol" to it.communicationProtocol,
                    "bleMac" to it.mac,
                    "bleName" to it.bluetoothName,
                )
                BluetoothManager.sentryPushListener?.invoke(
                    PushLogLevel.INFO,
                    "ble_api_connect",
                    map.vbToJson()
                )
            }, error = onError, showErrorToast = false)
        }
    }


    /**
     * 解绑设备
     * @param deviceUserRelId 用户设备关联id
     */
    fun unBindDevice(
        deviceUserRelId: String,
        onSuccess: () -> Unit
    ) {
        request<Boolean>({
            MrkNetwork.instance.put(
                "/app/device-user-rel/disconnectionDevice",
                mapOf(
                    "deviceUserRelId" to deviceUserRelId
                )
            )
        }, success = {
            onSuccess.invoke()
        }, showErrorToast = false)
    }


    /**
     * 获取该用户所有设备
     * @param productType 产品分类 1.运动设备，3健康设备
     * @param productId 产品id
     */
    fun getDeviceMyBindBean(
        productType: String = "",
        productId: String = "",
        onSuccess: (List<DeviceMyBindBean.Record>) -> Unit
    ) {
        val map = mutableMapOf<String, Any>()
        map["productType"] = productType
        map["productId"] = productId

        request<DeviceMyBindBean>({
            MrkNetwork.instance.post("/app/device-user-rel/pageMyDevices", map)
        }, success = {
            onSuccess.invoke(it.records)
        }, showErrorToast = false)
    }

    /**
     * 获取最新固件版本
     * @param unitDistance 单位  单位类型：1-公制，2-英制
     */
    fun getDeviceOtaBean(
        bean: DeviceDetailsBean,
        unitDistance: Int,
        modelNumber: String,
        modelRevision: String,
        onSuccess: (DeviceOtaBean) -> Unit,
        onError: (String) -> Unit
    ) {

        val map = mutableMapOf<String, Any>()
        map["deviceUserRelId"] = bean.deviceUserRelId
        map["modelId"] = bean.modelId
        map["moduleNumber"] = modelNumber
        map["currentVersion"] = modelRevision
        map["bluetoothName"] = bean.bluetoothName
        map["mac"] = bean.mac
        map["isRecordLog"] = 1
        map["unitType"] = unitDistance
        request({
            MrkNetwork.instance.get("/app/product-model-firmware/getLatestFirmwareVersion", map)
        }, success = onSuccess, error = onError, showErrorToast = false)

    }

    /**
     * 记录固件升级日志
     * @param deviceUserRelId 用户id
     * @param firmwareId 版本号
     */
    fun saveDeviceOtaLog(
        deviceUserRelId: String,
        firmwareId: String
    ) {

        val map = mutableMapOf<String, Any>()
        map["deviceUserRelId"] = deviceUserRelId
        map["firmwareId"] = firmwareId
        request<DeviceOtaBean>({
            MrkNetwork.instance.post("/app/product-model-firmware/setLog", map)
        }, success = {

        }, showErrorToast = false)

    }

}