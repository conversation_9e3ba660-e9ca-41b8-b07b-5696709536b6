package com.mrk.device.utils

import com.cc.control.bean.CharacteristicBean
import com.cc.control.vbToBean
import com.cc.control.vbToList
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.DeviceDetailsBean
import com.mrk.device.bean.DeviceGoConnectBean
import com.mrk.device.bean.DeviceSearchBean
import com.mrk.device.bean.LocalDataBean
import com.mrk.device.device.DeviceMangerDispose
import com.v.log.util.log
import com.v.log.util.logD
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException


/**
 * author  : ww
 * desc    : 获取本地协议工具类
 * time    : 2024/5/24 14:02
 */
object LocalDataUtil {

    suspend fun readSearchData(): HashMap<String, ArrayList<DeviceSearchBean>> {
        val result = readJsonFromAssets("search_data.json")
        val list = result.vbToList(LocalDataBean::class.java)
        val map = HashMap<String, ArrayList<DeviceSearchBean>>()
        list.forEach {
            map[it.bluetoothName] = it.list
        }
        return map
    }


    /**
     * 遍历HashMap 的key
     */
    private fun isContains(searchString: String): String {
        // 遍历HashMap中的所有键
        for (key in DeviceMangerDispose.mViewModel.localData.keys) {
            // 检查当前键是否包含搜索字符串
            if (searchString.contains(key)) {
                return key // 如果包含，则返回true
            }
        }
        return "" // 如果没有键包含搜索字符串，则返回false
    }

    /**
     * 获取搜索对象
     */
    fun readSearch(bluetoothName: String): ArrayList<DeviceSearchBean> {
        val list = ArrayList<DeviceSearchBean>()
        val key = isContains(bluetoothName)
        if (key.isNotEmpty()) {
            DeviceMangerDispose.mViewModel.localData[key]?.run {
                for (i in 0 until this.size) {
                    if (bluetoothName.contains(this[i].bluetoothName)) {
                        list.add(this[i])
                    }
                }
            }
        }
        return list
    }

    /**
     * 获取连接对象
     */
    suspend fun readConnect(
        bean: DeviceGoConnectBean,
        characteristic: List<CharacteristicBean>
    ): DeviceDetailsBean? {
        var fileName = ""
        //获取特征值
        val characteristicBean = if (characteristic.isNotEmpty()) characteristic[0] else null
        val list = readSearch(bean.bluetoothName)
        val key = isContains(bean.bluetoothName)

        //如果搜索结果只有一条 或者 特征值为空 则直接返回
        if (list.size == 1 || characteristicBean == null) {
            //协议
            fileName = getConnectAssetsFileName(key, list[0].communicationProtocol)
        } else {
            for (i in 0 until list.size) {
                if (list[i].uniqueModelIdentify.isNotEmpty()) {
                    val uniqueModelIdentify = list[i].uniqueModelIdentify[0]
                    if (uniqueModelIdentify.characteristicValue == characteristicBean.characteristicValue) {
                        //协议
                        fileName = getConnectAssetsFileName(
                            key,
                            list[i].communicationProtocol
                        )
                        break
                    }

                }
            }

            if (fileName.isEmpty()) {
                fileName =
                    getConnectAssetsFileName(key, list[0].communicationProtocol)
            }
        }

        "fileName:$fileName".log()
        return if (fileName.isNotEmpty()) {
            val result = readJsonFromAssets(fileName)
            if (result.isNotEmpty()) {
                result.vbToBean(DeviceDetailsBean::class.java)
            } else {
                null
            }
        } else {
            null
        }
    }

    /**
     * 获取连接对象的json
     * @param bluetoothName 蓝牙名称
     * @param communicationProtocol 协议code
     * @return 协议json
     */
    private fun getConnectAssetsFileName(
        bluetoothName: String,
        communicationProtocol: Int
    ): String {
        "通信协议:${MrkDeviceManger.getProtocolName(communicationProtocol)}".log()
        //通信协议	1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健 3
        return when (communicationProtocol) {
            3 -> {
                "${bluetoothName}_Connect_ZJ.json"
            }

            2 -> {
                "${bluetoothName}_Connect_FTMS.json"
            }

            else -> {
                ""
            }
        }

    }

    /**
     * // 示例调用：
     * // launch {
     * //     val jsonString = readJsonFromAssets(context, "your_json_file.json")
     * //     // 在此处处理 JSON 字符串，例如解析为对象等
     * // }
     */
    private suspend fun readJsonFromAssets(fileName: String): String {
        return withContext(Dispatchers.IO) {
            try {
                // 打开 Assets 文件夹中的文件
                val inputStream = MrkDeviceManger.application.assets.open(fileName)
                // 读取文件内容并转换为字符串
                inputStream.bufferedReader().use {
                    it.readText()
                }
            } catch (e: IOException) {
                // 处理异常情况，例如文件不存在等
                e.printStackTrace()
                ""
            }
        }
    }
}