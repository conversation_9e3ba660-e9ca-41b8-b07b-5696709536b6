package com.mrk.device.utils

import android.content.Context
import com.liulishuo.okdownload.DownloadTask
import com.liulishuo.okdownload.SpeedCalculator
import com.liulishuo.okdownload.core.Util
import com.liulishuo.okdownload.core.breakpoint.BlockInfo
import com.liulishuo.okdownload.core.breakpoint.BreakpointInfo
import com.liulishuo.okdownload.core.cause.EndCause
import com.liulishuo.okdownload.core.listener.DownloadListener4WithSpeed
import com.liulishuo.okdownload.core.listener.assist.Listener4SpeedAssistExtend
import com.mrk.device.listener.DownloadListener
import com.v.log.util.log
import java.io.File

/**
 * author  : ww
 * desc    :  下载
 * time    : 2024/1/19 15:34
 */
object DownloadUtil {

     fun download(context: Context, url: String, listener: DownloadListener) {
        val parentFile = File(context.getExternalFilesDir(null)!!.absolutePath) //源文件或者目录
        val downloadTask = DownloadTask.Builder(url, parentFile)
            .setFilenameFromResponse(true)//是否使用 response header or url path 作为文件名，此时会忽略指定的文件名，默认false
            .setMinIntervalMillisCallbackProcess(300) // 回调时间
            .setConnectionCount(1)//使用单线程下载 不然会导致下载不成功的问题
            .build()

        var totalLength: Long = 0
        var readableTotalLength = ""
        downloadTask.enqueue(object : DownloadListener4WithSpeed() {
            override fun taskStart(task: DownloadTask) {
                "downloadStart".log()
                listener.onStart()
            }

            override fun connectStart(
                task: DownloadTask,
                blockIndex: Int,
                requestHeaderFields: MutableMap<String, MutableList<String>>
            ) {
            }

            override fun connectEnd(
                task: DownloadTask,
                blockIndex: Int,
                responseCode: Int,
                responseHeaderFields: MutableMap<String, MutableList<String>>
            ) {
            }

            override fun taskEnd(
                task: DownloadTask,
                cause: EndCause,
                realCause: Exception?,
                taskSpeed: SpeedCalculator
            ) {
                "downloadEnd:${task.file}".log()
                if (task.file == null) {
                    listener.onError()
                } else {
                    listener.onDownloadOtaEnd(task.file.toString())
                }

            }

            override fun infoReady(
                task: DownloadTask,
                info: BreakpointInfo,
                fromBreakpoint: Boolean,
                model: Listener4SpeedAssistExtend.Listener4SpeedModel
            ) {
                totalLength = info.totalLength;
                readableTotalLength = Util.humanReadableBytes(totalLength, true)
            }

            override fun progressBlock(
                task: DownloadTask,
                blockIndex: Int,
                currentBlockOffset: Long,
                blockSpeed: SpeedCalculator
            ) {
            }

            override fun progress(
                task: DownloadTask,
                currentOffset: Long,
                taskSpeed: SpeedCalculator
            ) {
                val readableOffset = Util.humanReadableBytes(currentOffset, true)
                val progressStatus = "$readableOffset/$readableTotalLength"
                val speed = taskSpeed.speed()
                val percent = (currentOffset.toFloat() / totalLength * 100).toInt()

                listener.onDownloadOtaProgress(readableOffset, readableTotalLength, speed, percent)
                "[$progressStatus]，速度：$speed，进度：$percent%".log()
            }

            override fun blockEnd(
                task: DownloadTask,
                blockIndex: Int,
                info: BlockInfo?,
                blockSpeed: SpeedCalculator
            ) {
            }
        })

    }
}