package com.mrk.device.listener


/**
 * author  : ww
 * desc    :
 * time    : 2023/10/20 10:48
 */
interface DownloadListener {
    //开始
    fun onStart()

    //ota文件下载进度
    fun onDownloadOtaProgress(
        readableOffset: String,//当前下载的文件大小
        readableTotalLength: String,//总共需要下载的文件大小
        speed: String,//下载速度
        progress: Int//下载进度(最大100)
    )

    //ota文件下载完成
    fun onDownloadOtaEnd(path: String)

    //错误
    fun onError()

}