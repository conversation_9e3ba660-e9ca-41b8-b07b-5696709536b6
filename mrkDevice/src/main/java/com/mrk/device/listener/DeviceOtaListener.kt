package com.mrk.device.listener


/**
 * author  : ww
 * desc    :
 * time    : 2023/10/20 10:48
 */
interface DeviceOtaListener {

    //开始
    fun onStart()

    //ota文件下载进度
    fun onDownloadOtaProgress(
        readableOffset: String,//当前下载的文件大小
        readableTotalLength: String,//总共需要下载的文件大小
        speed: String,//下载速度
        progress: Int//下载进度(最大100)
    )

    //ota安装进度
    fun onInstallOtaProgress(progress: Int)

    //ota安装完成
    fun onInstallOtaEnd()

    //错误
    fun onError()


    //当前为最新版本 无需更新
    fun onTheLatestVersion()
}