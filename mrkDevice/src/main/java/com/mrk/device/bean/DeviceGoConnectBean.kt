package com.mrk.device.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * author  : ww
 * desc    : 需要连接的设备对象
 * time    : 2024/2/4 16:28
 */
@Parcelize
data class DeviceGoConnectBean(
    var mac: String,//mac地址  A4:91:F7:81:74:C6
    val productId: String,//设备大类 6
    val bluetoothName: String,//蓝牙名称 MRK-E19-9354
    val modelId: String,//型号id 1630452448293478401
    val uniqueModelIdentify: List<DeviceSearchBean.UniqueModelIdentify> = listOf(),//唯一型号确认json
): Parcelable