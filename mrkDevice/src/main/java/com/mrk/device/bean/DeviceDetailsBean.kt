package com.mrk.device.bean

/**
 * author  : ww
 * desc    : 设备连接成功返回的对象
 * time    : 2023/10/20 16:47
 */
data class DeviceDetailsBean(
    val productId: String = "", //产品Id	  // 1
    val modelId: String = "", //型号id	 1744914228939124742
    val modelName: String = "",//型号名称
    val cover: String = "", //型号封面	 https://static.merach.com/other/20240201/c70321dd6842484f8855d369449e4e19_1200x900.jpg
    val communicationProtocol: Int = 0, //通信协议	1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健 3
    val isOta: Int = 0, //是否支持ota,不推荐使用，建议使用productModelTsl中的isOta 「已废弃」 1
    val otaProtocol: Int = 0, //ota协议	1:泰凌威 智建设备；2:博通; 3:DFU 4:新向远 5富芮坤 6 凌思威	  0
    val versionEigenValue: Int = 0, //版本特征值	 2
    val deviceUserRelId: String = "", //用户设备关联id	 1752946454528196610
    val deviceAlias: String = "",//设备别名
    var bluetoothName: String = "", //蓝牙广播名	 MRK-S12-07BD
    var mac: String = "", //mac地址	 24:00:0C:A0:DA:27
    val productName: String = "",//产品名称
    val productType: Int = 0, //产品分类	 0
    val helpCenter: String = "",//帮助中心
    val productManual: String = "",//产品说明
    val isMerit: Int = 0, //是否merit设备,不推荐使用，建议使用productModelTsl中的isElectromagneticControl 1
    val deviceId: String = "", //设备id	 0
    val firmwareVersion: String = "",//当前固件版本
    val isBind: Int = 0, //是否绑定操作	 0
    val firstBind: Int = 0,//0-非第一次绑定  1-第一次绑定
    val isNewBindDevice: Int = 0, // 0 or null-非新绑定的设备 1-新绑定的设备
    val equipImageGif: String = "",//设备的gif图片
    val productModelTsl: ProductModelTsl = ProductModelTsl()
) {
    data class ProductModelTsl(
        val isOta: Int = 0, //是否支持ota：1是0否	 1
        val isClean: Int = 0, //是否支持清理运动数据：1是0否	 1
        val isElectromagneticControl: Int = 0, //是否是电磁控设备 1是0否	 1
        val versionEigenValue: Int = 0, //版本特征值	 2
        val controlResistance: Int = 0, //是否控制阻力，1是0否	 0
        val minResistance: Int = 0, //最小阻力	 0
        val maxResistance: Int = 0, //最大阻力	 0
        val controlSpeed: Int = 0, //是否控制速度，1是0否	 0
        val minSpeed: Int = 0, //最小速度	 0
        val maxSpeed: Int = 0, //最大速度	 0
        val controlSlope: Int = 0, //是否控制坡度，1是0否	 0
        val minSlope: Int = 0, //最小坡度	 0
        val maxSlope: Int = 0, //最大坡度	 0
        val controlGear: Int = 0, //是否可调节档位，1是0否	 0
        val minGear: Int = 0, //最小档位	 0
        val maxGear: Int = 0, //最大档位	 0
        val isSupportResistanceEcho: Int = 0, //是否支持阻力回显	 1
        val guide: String = "",//引导信息
        val electrode: Int = 0, //电极信息	 0
        val unitVal: Int = 0, //设备单位：1-公制，2-英制	 1
        val isSupportSlopeEcho: Int = 0, //是否支持坡度回显：0-否，1-是	 0
        val torqueMap: HashMap<String, Double> = HashMap()//扭矩
    )
}

