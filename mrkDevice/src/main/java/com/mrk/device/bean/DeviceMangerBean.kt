package com.mrk.device.bean

import com.cc.control.BaseDeviceFunction

/**
 * author  : ww
 * desc    : 所有操作的设备
 * time    : 2024/1/12 15:26
 */
data class DeviceMangerBean(
    var connectBean: DeviceGoConnectBean,//当前连接的对象
    var deviceOtaBean: DeviceOtaBean? = null,//当前设备的ota对象
    var deviceDetails: DeviceDetailsBean? = null,//当前设备的设备详情(需要先连接)
    var connectEnum: DeviceConnectEnum = DeviceConnectEnum.OFF, //当前设备的连接状态
    var deviceFunction: BaseDeviceFunction? = null,//当前设备的设备控制类
    var unitDistance: Int = -1//设备单位：1-公制，2-英制	 1
)
