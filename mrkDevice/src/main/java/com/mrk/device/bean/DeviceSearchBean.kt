package com.mrk.device.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * author  : ww
 * desc    : 搜索到的设备
 * time    : 2023/10/20 16:47
 */
@Parcelize
data class DeviceSearchBean(
    val modelId: String = "", //型号id 1630452448293478401
    val originModelId: String = "", //起源型号id(老型号ID) 1630452448293478401
    val modelName: String = "", //型号名称 S06单车
    var bluetoothName: String = "", //蓝牙广播名 MRK-S06-07BD
    var mac: String = "", //mac地址	 24:00:0C:A0:DA:27
    val modelDescription: String = "",  //型号描述
    val productId: String = "", //产品Id	 1
    val cover: String = "", //型号封面	 https://static.merach.com/other/20230228/24843ca5b8cd4b9ebba0106cb667f161_1560x840.png
    val communicationProtocol: Int = 0, //通信协议	1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健
    val isOta: Int = 0, //是否支持ota	 1
    val isMerit: Int = 0, //是否merit设备	 1
    val uniqueModelIdentify: List<UniqueModelIdentify> = listOf(),//唯一型号确认json
    val isTrust: Int = 0,//是否可以信任的数据，0不可信任，1可信任，当数据不可信任时不展示具体信息 1
    var connectEnum: DeviceConnectEnum? //自定义的连接状态
): Parcelable {
    @Parcelize
    data class UniqueModelIdentify(
        var service: String = "",//蓝牙服务
        var characteristicProperties: String = "",//特征值的属性
        var characteristicValue: String = "",//特征值的值
    ) : Parcelable
}
