package com.mrk.device.bean

/**
 * author  : ww
 * desc    :
 * time    : 2024/1/18 16:00
 */
data class DeviceOtaBean(
    var firmwareId: String = "",//更新固件标识ID
    var productId: Int = 0,//产品id
    var productModelId: Int = 0,//产品型号id
    var moduleNumber: String = "",//模块编号
    var versionNumber: String = "",//版本号
    var downloadLink: String = "",//下载链接
    var updateLog: String = "",//更新日志
    var updateType: Int = 0,//更新类型1:选择更新，2:强制更新
    var isPop: Int = 0,//是否弹窗 1是0否
    var otaProtocol: Int = 0,//ota协议：1.泰凌微 ，2.博通，3.DFU，4.新向远，5.富芮坤，6.凌思微
    var isOta: Int = 0,//是否支持ota
    var isLastVersion: Int = 1,//是最新版本
    var mac: String = "",//当前操作的mac
    var modelName: String = "",//模块名称X1
    var bluetoothName: String = "",//蓝牙名称
    var deviceAlias: String = "",//别名
    var deviceUserRelId: String = "",//该设备的用户id
    var currentVersionNumber: String = "",//当前连接设备的固件版本号(客户端自己写进去的)
)