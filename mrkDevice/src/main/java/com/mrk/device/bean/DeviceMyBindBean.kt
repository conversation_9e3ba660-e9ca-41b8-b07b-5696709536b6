package com.mrk.device.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * author  : ww
 * desc    : 我绑定的设备
 * time    : 2023/10/20 16:47
 */
@Parcelize
data class DeviceMyBindBean(
    val records: List<Record> = listOf(),
    val total: String = "", // 10
    val size: String = "", // 10
    val current: String = "", // 1
    val countId: String = "",
    val maxLimit: String = "", // 0
    val searchCount: Boolean = false, // true
    val pages: String = "" // 1
): Parcelable {
    @Parcelize
    data class Record(
        val productId: String = "", // 1
        val productName: String = "", // 动感单车
        val productType: Int = 0, // 1
        val modelId: String = "", // 1630452448293478401
        val modelName: String = "", // S06单车
        val cover: String = "", // https://static.merach.com/other/20230228/24843ca5b8cd4b9ebba0106cb667f161_1560x840.png
        val communicationProtocol: Int = 0, // 3
        val helpCenter: String = "",
        val productManual: String = "",
        val deviceId: String = "", // 51858
        val deviceUserRelId: String = "", // 1676883048598306817
        val deviceAlias: String = "",
        val bluetoothName: String = "", // MRK-S06-07BD
        var mac: String = "", // 24:00:0C:A0:DA:27
        val firmwareVersion: String = "", // 2.0.7
        val description: String = "",
        val productModelTsl: ProductModelTsl? = ProductModelTsl(),
        val brandType: Int = 0, // 1
        val uniqueModelIdentify: List<DeviceSearchBean.UniqueModelIdentify> = listOf(),//唯一型号确认json
    ): Parcelable {
        @Parcelize
        data class ProductModelTsl(
            val isOta: Int = 0, // 1
            val isClean: Int = 0, // 1
            val isElectromagneticControl: Int = 0, // 1
            val versionEigenValue: Int = 0, // 2
            val controlResistance: Int = 0, // 1
            val minResistance: Int = 0, // 1
            val maxResistance: Int = 0, // 32
            val controlSpeed: Int = 0, // 0
            val minSpeed: Int = 0, // 0
            val maxSpeed: Int = 0, // 0
            val controlSlope: Int = 0, // 0
            val minSlope: Int = 0, // 0
            val maxSlope: Int = 0, // 0
            val controlGear: Int = 0, // 0
            val minGear: Int = 0, // 0
            val maxGear: Int = 0, // 0
            val isSupportResistanceEcho: Int = 0, // 1
            val guide: String = "",
            val electrode: Int = 0, // 0
            val unitVal: Int = 0, // 1
            val isSupportSlopeEcho: Int = 0 // 1
        ): Parcelable
    }

}