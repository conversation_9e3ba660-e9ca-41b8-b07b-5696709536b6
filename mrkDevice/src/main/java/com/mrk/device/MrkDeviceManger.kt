package com.mrk.device

import android.app.Application
import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.cc.control.BluetoothManager
import com.cc.control.enums.PushLogLevel
import com.cc.control.protocol.DeviceConstants
import com.mrk.device.bean.BluetoothEnum
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceGoConnectBean
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceMyBindBean
import com.mrk.device.bean.DeviceSearchBean
import com.mrk.device.device.DeviceControl
import com.mrk.device.device.DeviceListener
import com.mrk.device.device.DeviceMangerDispose
import com.permissionx.guolindev.request.ForwardScope
import com.v.log.LogConfig
import com.v.log.VLog


/**
 * author  : ww
 * desc    : 对外的方法
 * time    : 2023/10/20 10:27
 */
object MrkDeviceManger {

    //是否使用本地assets里面的数据
    var isLocalData: Boolean = false
    lateinit var application: Application

    //在Application里面初始化
    fun init(application: Application, showLog: Boolean = false, isLocalData: Boolean = false) {
        MrkDeviceManger.isLocalData = isLocalData
        MrkDeviceManger.application = application
        BluetoothManager.initDeviceOption(application, showLog)
        VLog.init(LogConfig(application, showLog, true))
        DeviceMangerDispose.init(application)
        //网络请求配置
//        val netOptions = MrkNetOptions.Builder()
//            .setBaseUrl("http://115.236.10.188:7000/")
//            .setInterceptor(NetworkHeadInterceptor())
//            .build()
//        MrkNetworkConfig.init(netOptions)

        DeviceMangerDispose.showLog("init")
    }

    /**
     * 监听蓝牙开关状态
     */
    fun registerBluetoothStateListener(onBluetoothStatus: (BluetoothEnum) -> Unit): MrkDeviceManger {
        DeviceMangerDispose.registerBluetoothStateListener(onBluetoothStatus)
        return this
    }

    /**
     * Sentry log 埋点
     */
    fun registerSentryLogListener(sentryLogListener: (logLevel: PushLogLevel, message: String, extra: String) -> Unit): MrkDeviceManger {
        BluetoothManager.registerSentryLogListener(sentryLogListener)
        return this
    }

    /**
     * sentry 取消订阅回调
     */
    fun unRegisterSentryLogListener() {
        BluetoothManager.unRegisterSentryLogListener()
    }

    /**
     * 监听所有设备的状态
     */
    fun registerDeviceListener(
        context: FragmentActivity,
        listener: DeviceListener
    ): MrkDeviceManger {
        DeviceMangerDispose.addDeviceListener(context, listener)
        return this
    }

    /**
     * 创建
     *  @param params 可以是设备大类  也可以mac地址
     */
    fun create(
        context: FragmentActivity,
        params: String
    ): DeviceControl {
        return DeviceMangerDispose.create(
            context,
            params
        )
    }


    /**
     * 通过搜索对象创建
     */
    fun create(
        context: FragmentActivity,
        bean: DeviceSearchBean,
    ): DeviceControl {
        return create(
            context,
            formatDeviceGoConnectBean(bean)
        )
    }

    /**
     * 通过绑定的设备对象创建
     */
    fun create(
        context: FragmentActivity,
        bean: DeviceMyBindBean.Record,
    ): DeviceControl {
        return create(
            context,
            formatDeviceGoConnectBean(bean)
        )
    }

    /**
     * 通过连接的对象返回创建
     */
    fun create(
        context: FragmentActivity,
        bean: DeviceGoConnectBean,
    ): DeviceControl {
        return DeviceMangerDispose.create(
            context,
            bean
        )
    }

    /**
     * 开始搜索设备
     * @param onSearchStatus 搜索状态回调
     * @param onSearchDevice 搜索到的设备回调
     */
    fun startSearch(
        context: FragmentActivity,
        onSearchStatus: ((BluetoothEnum) -> Unit),
        onSearchDevice: ((DeviceSearchBean) -> Unit),
    ) {

        DeviceMangerDispose.startSearch(
            context = context,
            showType = 1,
            onSearchStatus = onSearchStatus,
            onSearchDevice = onSearchDevice,
        )

    }


    /**
     * 开始搜索设备
     * @param bluetoothName 根据蓝牙名匹配指定单个设备名称
     * @param productId 产品id
     * @param modelId 型号id
     * @param notShowProductIdList 不显示的产品id集合
     * @param showBindDevice 是否展示已绑定设备 1显示 0不显示
     * @param showType 显示类型，1显示运动设备，3显示健康设备
     * @param onSearchStatus 搜索状态回调
     * @param onSearchDevice 搜索到的设备回调
     * @param onForwardToSettings 第二次拒绝授权后的弹窗
     * @param onPermissionError 授权失败
     */
    fun startSearch(
        context: FragmentActivity,
        bluetoothName: String = "",
        productId: String? = null,
        modelId: String? = null,
        showType: Int? = null,
        notShowProductIdList: IntArray? = null,
        showBindDevice: Int? = null,
        onSearchStatus: ((BluetoothEnum) -> Unit),
        onSearchDevice: ((DeviceSearchBean) -> Unit),
        onBluetoothClose: (() -> Unit)? = null,
        onForwardToSettings: ((scope: ForwardScope, deniedList: List<String>) -> Unit)? = null,
        onPermissionError: ((grantedList: List<String>) -> Unit)? = null
    ) {

        DeviceMangerDispose.startSearch(
            context,
            bluetoothName,
            productId,
            modelId,
            showType,
            notShowProductIdList,
            showBindDevice,
            onSearchStatus,
            onSearchDevice,
            onBluetoothClose,
            onForwardToSettings,
            onPermissionError
        )

    }


    /**
     * 关闭搜索
     */
    fun stopSearch() {
        DeviceMangerDispose.stopSearch()
    }

    /**
     *  手机蓝牙开关转态
     */
    fun getBluetoothStatus(): Boolean {
        return DeviceMangerDispose.getBluetoothStatus()
    }

    /**
     *  打开蓝牙
     */
    fun openBluetooth(
        context: FragmentActivity,
        onBluetoothStatusListener: ((Boolean) -> Unit)? = null
    ) {
        DeviceMangerDispose.openBluetooth(context, onBluetoothStatusListener)
    }


    /**
     * 获取保存的对象
     * @param params 可以是设备大类  也可以mac地址  也可以蓝牙名称
     */
    fun getDeviceMangerBean(params: String): DeviceMangerBean? {
        return DeviceMangerDispose.getDeviceMangerBean(params)
    }

    /**
     * 获取设备状态
     * @param params 可以是设备大类  也可以mac地址
     */
    fun getDeviceStatus(params: String): DeviceConnectEnum {
        return DeviceMangerDispose.getDeviceStatus(params)
    }


    /**
     * 判断当前是否有连接
     * @param params 可以是设备大类  也可以mac地址
     */
    fun isConnect(params: String): Boolean {
        return DeviceMangerDispose.isConnect(params)
    }


    /**
     * 断开连接
     * @param params 可以是设备大类  也可以mac地址
     */
    fun disConnect(params: String, onSuccess: ((mac: String) -> Unit)? = null) {
        DeviceMangerDispose.disConnect(params, onSuccess)
    }


    /**
     * 解绑设备
     * @param mac 设备mac地址
     * @param deviceUserRelId 用户设备关联id
     */
    fun unBindDevice(
        context: FragmentActivity,
        mac: String,
        deviceUserRelId: String,
        onSuccess: () -> Unit
    ) {
        DeviceMangerDispose.unBindDevice(context, mac, deviceUserRelId, onSuccess)
    }

    /**
     * 获取该用户绑定的设备列表
     * @param productType 产品分类 1.运动设备，3健康设备
     * @param productId 产品id
     */
    fun getDeviceMyBindList(
        productType: String = "",
        productId: String = "",
        onSuccess: (List<DeviceMyBindBean.Record>) -> Unit
    ) {
        DeviceMangerDispose.getDeviceMyBindList(productType, productId, onSuccess)
    }

    /**
     * 通过传入的mac地址获取绑定的设备对象
     * @param mac 地址(必须是mac地址,因为可能绑定很多台同样类型的设备)
     */
    fun getDeviceMyBindBean(mac: String): DeviceMyBindBean.Record? {
        return DeviceMangerDispose.getDeviceMyBindBean(mac)
    }

    /**
     * 获取所有保存的对象Map
     */
    fun getDeviceMap(): HashMap<String, DeviceMangerBean> {
        return DeviceMangerDispose.getDeviceMap()
    }

    /**
     * 获取设备的可连接对象
     */
    fun getDeviceGoConnectBean(params: String): DeviceGoConnectBean? {
        return DeviceMangerDispose.getDeviceGoConnectBean(params)
    }


    /**
     * 释放数据
     * @param mac 为空则表示释放所有
     */
    fun clear(context: Context, mac: String? = null) {
        DeviceMangerDispose.clear(context, mac)
    }

    /**
     * 把当前的我绑定设备对象转换成需要连接的对象
     */
    fun formatDeviceGoConnectBean(bean: DeviceMyBindBean.Record): DeviceGoConnectBean {
        return DeviceGoConnectBean(
            mac = bean.mac, productId = bean.productId, bluetoothName = bean.bluetoothName,
            modelId = bean.modelId, uniqueModelIdentify = bean.uniqueModelIdentify
        )
    }

    /**
     * 把当前的搜索对象转换成 需要连接的对象
     */
    fun formatDeviceGoConnectBean(bean: DeviceSearchBean): DeviceGoConnectBean {
        return DeviceGoConnectBean(
            mac = bean.mac, productId = bean.productId, bluetoothName = bean.bluetoothName,
            modelId = bean.modelId, uniqueModelIdentify = bean.uniqueModelIdentify
        )
    }

    /**
     * 通过设备大类id 返回对象的设备名称
     * @param productId 设备大类id
     * @return 大类名称
     */
    fun getTypeName(productId: String): String {
        return when (productId) {
            DeviceConstants.D_BICYCLE -> "动感单车"
            DeviceConstants.D_TREADMILL -> "跑步机"
            DeviceConstants.D_OTHER -> "其他"
            DeviceConstants.D_ROW -> "划船机"
            DeviceConstants.D_TECHNOGYM -> "椭圆机"
            DeviceConstants.D_FASCIA_GUN -> "筋膜枪"
            DeviceConstants.D_SKIPPING -> "跳绳"
            DeviceConstants.D_POWER -> "力量站"
            DeviceConstants.D_HULA_HOOP -> "呼啦圈"
            DeviceConstants.D_HEART -> "心率带"
            DeviceConstants.D_FAT_SCALE -> "体脂秤"
            DeviceConstants.D_PHYLLISRODS -> "飞力士棒"
            DeviceConstants.D_SCALE_LF -> "乐福体脂秤"
            DeviceConstants.D_SCALE_WL -> "沃莱体脂秤"
            DeviceConstants.D_GAME_PAD -> "游戏手柄"
            else -> {
                "未知设备"
            }
        }
    }

    /**
     * 获取对应协议名称
     * @param protocolCode 协议code
     * @return 协议名称
     */
    fun getProtocolName(protocolCode: Int): String {
        //通信协议	1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健 3
        return when (protocolCode) {
            1 -> "麦瑞克"
            2 -> "FTMS"
            3 -> "智健"
            4 -> "柏群"
            5 -> "FTMS+智健"
            else -> {
                "未知"
            }
        }
    }
}