plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id 'maven-publish'
    id 'com.kezong.fat-aar'
}
apply from: "upload.gradle"


android {
    namespace 'com.mrk.device'
    compileSdk AndroidVersions.compileSdkVersion

    defaultConfig {
        minSdk AndroidVersions.minSdkVersion
        targetSdk AndroidVersions.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    api "androidx.appcompat:appcompat:${Dependencies.appcompat}"
    api "androidx.lifecycle:lifecycle-viewmodel-ktx:${Dependencies.lifecycleViewModelKtx}"
    api "androidx.lifecycle:lifecycle-runtime-ktx:${Dependencies.lifecycleRuntimeKtx}"
    api "androidx.lifecycle:lifecycle-common-java8:${Dependencies.lifecycleCommonKtx}"
    api "com.guolindev.permissionx:permissionx:${Dependencies.permissionX}"//权限库
    api "com.github.oooo7777777:Vlog:${Dependencies.log}"   //日志打印
    implementation "com.liulishuo.okdownload:okdownload:1.0.7"//下载库
    implementation "com.liulishuo.okdownload:okhttp:1.0.7"//下载库

    api project(':mrkNetwork')
    api project(':mrkBluetooth')
}